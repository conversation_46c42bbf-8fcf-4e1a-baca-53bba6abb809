#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一路径管理器 v2.0（支持复权数据）

提供系统级别的统一路径管理功能，支持原始数据和复权数据的分区存储。

主要功能：
1. 统一的路径构建和验证（支持raw/和adjusted/目录）
2. 期货与股票的差异化路径处理
3. 复权数据路径管理（前复权/后复权）
4. 智能路径检测和修复
5. 路径缓存和性能优化
6. 配置管理和环境变量支持

数据结构：
- raw/: 原始数据（股票+期货）
- adjusted/: 复权数据（仅股票）

版本: v2.0
作者: Augment AI
日期: 2025-01-29
"""

import os
import glob
import threading
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime
from pathlib import Path

from utils.logger import get_unified_logger, LogTarget

# 创建统一日志记录器
logger = get_unified_logger(__name__, enhanced=True)


class PathManagerError(Exception):
    """路径管理器异常"""
    pass


class PathManagerV2:
    """
    统一路径管理器 v2.0 - 单例模式
    
    支持原始数据和复权数据的分区存储路径管理。
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化路径管理器"""
        if hasattr(self, '_initialized'):
            return
            
        # 从配置获取数据根目录
        from config.settings import DATA_ROOT
        self._data_root = DATA_ROOT
        
        # 分区规则配置
        self._partition_rules = {
            'tick': {
                'pattern': '{data_root}/{data_type}/{market}/{code}/{period}/{year}/{month:02d}/{day:02d}.parquet',
                'description': 'tick数据按日分区'
            },
            'default': {
                'pattern': '{data_root}/{data_type}/{market}/{code}/{period}/{year}.parquet',
                'description': '其他周期按年分区'
            }
        }
        
        # 支持的数据类型
        self._data_types = {
            'raw': 'raw',  # 原始数据
            'adjusted': 'adjusted'  # 复权数据
        }
        
        # 支持的复权类型
        self._adjustment_types = {
            'front': 'front',  # 前复权
            'back': 'back'     # 后复权
        }
        
        # 期货交易所列表（不支持复权）
        self._futures_exchanges = {'SF', 'DF', 'CF', 'ZF'}
        
        # 股票交易所列表（支持复权）
        self._stock_exchanges = {'SZ', 'SH'}
        
        self._initialized = True
        logger.debug(LogTarget.FILE, f"路径管理器v2.0初始化完成，数据根目录: {self._data_root}")
    
    def get_data_root(self) -> str:
        """获取数据根目录"""
        return self._data_root
    
    def set_data_root(self, data_root: str) -> None:
        """设置数据根目录"""
        self._data_root = data_root
        logger.info(LogTarget.FILE, f"数据根目录已更新: {data_root}")
    
    def parse_symbol(self, symbol: str) -> Tuple[str, str]:
        """
        解析股票代码
        
        Args:
            symbol: 股票代码，格式为 "code.market"
            
        Returns:
            Tuple[str, str]: (代码, 市场)
            
        Raises:
            PathManagerError: 当股票代码格式不正确时
        """
        try:
            if '.' not in symbol:
                raise PathManagerError(f"股票代码格式错误: {symbol}，应为 'code.market' 格式")
            
            code, market = symbol.split('.', 1)
            
            if not code or not market:
                raise PathManagerError(f"股票代码格式错误: {symbol}，代码或市场不能为空")
            
            # 验证市场代码
            all_exchanges = self._stock_exchanges | self._futures_exchanges
            if market not in all_exchanges:
                logger.warning(LogTarget.FILE, f"未知的市场代码: {market}")
            
            return code, market
            
        except Exception as e:
            raise PathManagerError(f"解析股票代码失败: {symbol} - {e}")
    
    def is_futures_symbol(self, symbol: str) -> bool:
        """
        判断是否为期货代码
        
        Args:
            symbol: 股票代码
            
        Returns:
            bool: 是否为期货代码
        """
        try:
            _, market = self.parse_symbol(symbol)
            return market in self._futures_exchanges
        except:
            return False
    
    def is_stock_symbol(self, symbol: str) -> bool:
        """
        判断是否为股票代码
        
        Args:
            symbol: 股票代码
            
        Returns:
            bool: 是否为股票代码
        """
        try:
            _, market = self.parse_symbol(symbol)
            return market in self._stock_exchanges
        except:
            return False
    
    def build_partitioned_path(
        self, 
        symbol: str, 
        period: str, 
        timestamp: Optional[Union[str, datetime]] = None,
        data_type: str = "raw",
        adj_type: Optional[str] = None
    ) -> str:
        """
        构建分区存储路径
        
        Args:
            symbol: 股票代码，格式为 "code.market"
            period: 数据周期
            timestamp: 时间戳，用于确定分区
            data_type: 数据类型，"raw"（原始数据）或"adjusted"（复权数据）
            adj_type: 复权类型，"front"（前复权）或"back"（后复权），仅当data_type="adjusted"时有效
            
        Returns:
            str: 分区存储路径
            
        Raises:
            PathManagerError: 当参数不正确时
        """
        try:
            # 解析股票代码
            code, market = self.parse_symbol(symbol)
            
            # 验证数据类型
            if data_type not in self._data_types:
                raise PathManagerError(f"不支持的数据类型: {data_type}")
            
            # 期货不支持复权数据
            if data_type == "adjusted" and self.is_futures_symbol(symbol):
                raise PathManagerError(f"期货不支持复权数据: {symbol}")
            
            # 复权数据必须指定复权类型
            if data_type == "adjusted":
                if adj_type is None:
                    raise PathManagerError("复权数据必须指定复权类型（front或back）")
                if adj_type not in self._adjustment_types:
                    raise PathManagerError(f"不支持的复权类型: {adj_type}")
            
            # 处理时间戳
            if timestamp is None:
                timestamp = datetime.now()
            elif isinstance(timestamp, str):
                # 尝试解析时间戳字符串
                if len(timestamp) == 8:  # YYYYMMDD
                    timestamp = datetime.strptime(timestamp, '%Y%m%d')
                elif len(timestamp) == 14:  # YYYYMMDDHHMMSS
                    timestamp = datetime.strptime(timestamp, '%Y%m%d%H%M%S')
                else:
                    raise PathManagerError(f"不支持的时间戳格式: {timestamp}")
            
            # 选择分区规则
            rule_key = 'tick' if period.lower() == 'tick' else 'default'
            rule = self._partition_rules[rule_key]
            
            # 构建路径变量
            path_vars = {
                'data_root': self._data_root,
                'data_type': data_type,
                'market': market,
                'code': code,
                'period': period,
                'year': timestamp.year,
                'month': timestamp.month,
                'day': timestamp.day
            }
            
            # 对于复权数据，需要在路径中插入复权类型（新格式：adj_type在market之前）
            if data_type == "adjusted":
                # 修改路径模式以包含复权类型 - 新格式：{data_type}/{adj_type}/{market}/{code}/{period}
                if rule_key == 'tick':
                    pattern = '{data_root}/{data_type}/{adj_type}/{market}/{code}/{period}/{year}/{month:02d}/{day:02d}.parquet'
                else:
                    pattern = '{data_root}/{data_type}/{adj_type}/{market}/{code}/{period}/{year}.parquet'
                path_vars['adj_type'] = adj_type
            else:
                pattern = rule['pattern']
            
            result_path = pattern.format(**path_vars)
            
            # 标准化路径分隔符
            result_path = os.path.normpath(result_path)
            
            logger.debug(LogTarget.FILE, f"构建分区路径: {symbol} {period} {data_type} -> {result_path}")
            return result_path
            
        except Exception as e:
            logger.error(LogTarget.FILE, f"构建分区路径失败: {symbol} {period} - {e}")
            raise PathManagerError(f"路径构建失败: {e}")
    
    def get_base_directory(self, symbol: str, period: str, data_type: str = "raw", adj_type: Optional[str] = None) -> str:
        """
        获取基础目录路径
        
        Args:
            symbol: 股票代码
            period: 数据周期
            data_type: 数据类型
            adj_type: 复权类型
            
        Returns:
            str: 基础目录路径
        """
        try:
            code, market = self.parse_symbol(symbol)

            if data_type == "adjusted" and adj_type:
                # 新格式：{data_type}/{adj_type}/{market}/{code}/{period}
                return os.path.join(self._data_root, data_type, adj_type, market, code, period)
            else:
                return os.path.join(self._data_root, data_type, market, code, period)
                
        except Exception as e:
            raise PathManagerError(f"获取基础目录失败: {e}")
    
    def validate_path(self, path: str) -> bool:
        """
        验证路径格式是否正确
        
        Args:
            path: 要验证的路径
            
        Returns:
            bool: 路径是否有效
        """
        try:
            # 基本路径检查
            if not path or not isinstance(path, str):
                return False
            
            # 检查是否包含数据根目录
            if not path.startswith(self._data_root):
                return False
            
            # 检查路径结构
            relative_path = os.path.relpath(path, self._data_root)
            parts = relative_path.split(os.sep)
            
            # 至少应该有: data_type/market/code/period
            if len(parts) < 4:
                return False
            
            # 验证数据类型
            data_type = parts[0]
            if data_type not in self._data_types:
                return False
            
            return True
            
        except Exception:
            return False
    
    def normalize_path(self, path: str) -> str:
        """
        标准化路径格式
        
        Args:
            path: 要标准化的路径
            
        Returns:
            str: 标准化后的路径
        """
        return os.path.normpath(path)


# 全局路径管理器实例
_path_manager_instance = None
_path_manager_lock = threading.Lock()


def get_path_manager() -> PathManagerV2:
    """获取全局路径管理器实例"""
    global _path_manager_instance
    if _path_manager_instance is None:
        with _path_manager_lock:
            if _path_manager_instance is None:
                _path_manager_instance = PathManagerV2()
    return _path_manager_instance


# 便捷函数
def get_data_root() -> str:
    """获取数据根目录"""
    return get_path_manager().get_data_root()


def parse_symbol(symbol: str) -> Tuple[str, str]:
    """解析股票代码"""
    return get_path_manager().parse_symbol(symbol)


def build_partitioned_path(
    symbol: str, 
    period: str, 
    timestamp: Optional[Union[str, datetime]] = None,
    data_type: str = "raw",
    adj_type: Optional[str] = None
) -> str:
    """构建分区存储路径"""
    return get_path_manager().build_partitioned_path(symbol, period, timestamp, data_type, adj_type)


def get_base_directory(symbol: str, period: str, data_type: str = "raw", adj_type: Optional[str] = None) -> str:
    """获取基础目录"""
    return get_path_manager().get_base_directory(symbol, period, data_type, adj_type)


def validate_path(path: str) -> bool:
    """验证路径"""
    return get_path_manager().validate_path(path)


def normalize_path(path: str) -> str:
    """标准化路径"""
    return get_path_manager().normalize_path(path)


def is_futures_symbol(symbol: str) -> bool:
    """判断是否为期货代码"""
    return get_path_manager().is_futures_symbol(symbol)


def is_stock_symbol(symbol: str) -> bool:
    """判断是否为股票代码"""
    return get_path_manager().is_stock_symbol(symbol)


def get_latest_partition_file(symbol: str, period: str, data_type: str = "raw", adj_type: Optional[str] = None) -> Optional[str]:
    """
    获取最新的分区文件路径

    Args:
        symbol: 股票代码，格式为 "code.market"
        period: 数据周期
        data_type: 数据类型，"raw"（原始数据）或"adjusted"（复权数据）
        adj_type: 复权类型，"front"（前复权）或"back"（后复权）

    Returns:
        Optional[str]: 最新分区文件路径，如果不存在则返回None
    """
    try:
        pm = get_path_manager()
        base_dir = pm.get_base_directory(symbol, period, data_type, adj_type)

        if not os.path.exists(base_dir):
            return None

        # 查找所有parquet文件
        pattern = os.path.join(base_dir, "*.parquet")
        files = glob.glob(pattern)

        if not files:
            return None

        # 按文件修改时间排序，返回最新的
        files.sort(key=os.path.getmtime, reverse=True)
        return files[0]

    except Exception as e:
        logger.error(LogTarget.FILE, f"获取最新分区文件失败 {symbol} {period}: {e}")
        return None


def get_earliest_partition_file(symbol: str, period: str, data_type: str = "raw", adj_type: Optional[str] = None) -> Optional[str]:
    """
    获取最早的分区文件路径

    Args:
        symbol: 股票代码，格式为 "code.market"
        period: 数据周期
        data_type: 数据类型，"raw"（原始数据）或"adjusted"（复权数据）
        adj_type: 复权类型，"front"（前复权）或"back"（后复权）

    Returns:
        Optional[str]: 最早分区文件路径，如果不存在则返回None
    """
    try:
        pm = get_path_manager()
        base_dir = pm.get_base_directory(symbol, period, data_type, adj_type)

        if not os.path.exists(base_dir):
            return None

        # 查找所有parquet文件
        pattern = os.path.join(base_dir, "*.parquet")
        files = glob.glob(pattern)

        if not files:
            return None

        # 按文件修改时间排序，返回最早的
        files.sort(key=os.path.getmtime)
        return files[0]

    except Exception as e:
        logger.error(LogTarget.FILE, f"获取最早分区文件失败 {symbol} {period}: {e}")
        return None
