#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量化数据读取模块

基于Pandas实现的高性能向量化数据读取框架，支持：
- 向量化多文件读取（pandas.concat实现）
- 多线程并行处理，结果按时间顺序排列
- 智能数据排序，确保输出时间有序
- 智能缓存和内存优化
- 完全保持数据原始结构

性能特点：
- 使用pandas原生向量化操作，完全保持数据原始性
- 支持多线程并行文件读取，自动按文件顺序重排结果
- 内存使用优化，支持大数据集处理
- 异步处理，减少I/O等待时间
- 自动数据排序验证，确保时间索引单调递增

数据顺序保证：
- 多线程读取后按输入文件顺序重新排列
- 数据合并后自动检查并排序时间索引
- 日志预览始终显示时间有序的数据

作者: AI Assistant
创建时间: 2025-07-15
更新时间: 2025-07-16
版本: 2.1.0 - 优化数据顺序处理
"""

import os
import asyncio
import time
from typing import List, Optional, Dict, Union, Any, Tuple
from pathlib import Path
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import functools
import hashlib

# 导入现有模块
from data.storage.path_manager import get_partition_files
from utils.logger import get_unified_logger
from utils.data_processor.index_monitor import monitor_index_format

logger = get_unified_logger(__name__)


def performance_monitor(func):
    """
    性能监控装饰器
    
    监控函数执行时间和数据处理量
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        # 执行函数
        result = func(*args, **kwargs)
        
        # 记录执行时间
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 记录性能日志
        if result is not None and hasattr(result, '__len__'):
            data_size = len(result) if hasattr(result, '__len__') else 0
            logger.debug(f"向量化数据读取: {func.__name__} 处理 {data_size} 条数据，耗时 {execution_time:.6f} 秒")
        else:
            logger.debug(f"向量化数据读取: {func.__name__} 耗时 {execution_time:.6f} 秒")
        
        return result
    return wrapper


class VectorizedDataReader:
    """
    向量化数据读取器

    提供高性能的向量化数据读取功能，支持多种优化策略。

    特性：
    - 多线程并行读取，结果按文件时间顺序排列
    - 智能数据排序，确保输出数据按时间索引有序
    - 完全保持原始数据结构，无任何数据修改
    - 支持缓存机制，提高重复查询性能
    - 自动数据完整性验证和错误处理

    数据顺序保证：
    - 多线程读取完成后，结果按输入文件顺序重新排列
    - pandas.concat后自动检查并排序，确保时间索引单调递增
    - 日志预览显示的数据始终按时间顺序排列
    """
    
    def __init__(self, enable_cache: bool = True, cache_size: int = 100):
        """
        初始化向量化数据读取器
        
        Args:
            enable_cache: 是否启用缓存
            cache_size: 缓存大小限制
        """
        self.enable_cache = enable_cache
        self.cache_size = cache_size
        self._cache = {} if enable_cache else None
        self._performance_stats = {
            'total_calls': 0,
            'total_time': 0.0,
            'cache_hits': 0,
            'total_files_read': 0,
            'total_data_size': 0
        }
        
        # 初始化完成
        logger.info("Pandas向量化读取器初始化成功")
    
    def _generate_cache_key(self, files: List[str], columns: Optional[List[str]] = None) -> str:
        """
        生成缓存键
        
        Args:
            files: 文件路径列表
            columns: 列名列表
            
        Returns:
            str: 缓存键
        """
        # 使用文件路径和修改时间生成缓存键
        file_info = []
        for file_path in files:
            if os.path.exists(file_path):
                mtime = os.path.getmtime(file_path)
                file_info.append(f"{file_path}:{mtime}")
        
        key_data = "|".join(file_info)
        if columns:
            key_data += f"|cols:{','.join(columns)}"
        
        return hashlib.md5(key_data.encode()).hexdigest()
    
    @performance_monitor
    def read_files_vectorized(self, files: List[str],
                            columns: Optional[List[str]] = None) -> Optional[pd.DataFrame]:
        """
        使用Pandas向量化读取多个parquet文件

        Args:
            files: 文件路径列表
            columns: 要读取的列名列表

        Returns:
            pd.DataFrame: 合并后的数据（完全保持原始结构）

        Raises:
            FileNotFoundError: 当文件不存在时
            ValueError: 当文件列表为空时
        """

        # 输入验证
        if not files:
            raise ValueError("文件列表不能为空")

        # 检查缓存
        cache_key = self._generate_cache_key(files, columns) if self.enable_cache else None
        if cache_key and cache_key in self._cache:
            logger.debug(f"缓存命中: {cache_key}")
            self._performance_stats['cache_hits'] += 1
            return self._cache[cache_key].copy()

        # 验证文件存在性
        missing_files = [f for f in files if not os.path.exists(f)]
        if missing_files:
            raise FileNotFoundError(f"以下文件不存在: {missing_files}")

        try:
            logger.debug(f"使用Pandas向量化读取 {len(files)} 个文件")

            # 执行向量化读取
            start_time = time.time()

            # 根据文件数量决定是否使用多线程
            if len(files) >= 3:
                # 多线程并行读取
                dfs = self._read_files_parallel(files, columns)
            else:
                # 单线程读取（小文件数量时避免线程开销）
                dfs = []
                for file_path in files:
                    df = pd.read_parquet(file_path, columns=columns)
                    if df is not None and not df.empty:
                        dfs.append(df)
                    else:
                        logger.warning(f"文件为空或读取失败: {file_path}")

            if not dfs:
                logger.warning("没有成功读取任何文件")
                return None

            # 使用统一的索引管理器进行安全合并
            from utils.data_processor.index_manager import IndexManager

            result_df = IndexManager.safe_concat(dfs, axis=0)

            if result_df is None:
                logger.error("统一索引管理器合并失败，使用传统方式")
                result_df = pd.concat(dfs, axis=0, ignore_index=False)

            # 确保数据按索引（时间）排序，提供一致的数据顺序
            if result_df is not None and not result_df.empty:
                # 验证索引格式
                IndexManager.log_index_info(result_df, "向量化读取合并后")

                if not result_df.index.is_monotonic_increasing:
                    logger.debug("数据索引未排序，正在按索引排序...")
                    result_df = result_df.sort_index()
                    logger.debug("数据索引排序完成")
                else:
                    logger.debug("数据索引已按时间排序")

            query_time = time.time() - start_time

            if result_df is not None and not result_df.empty:
                logger.debug(f"Pandas向量化读取源数据成功: {len(result_df)} 行数据，耗时 {query_time:.6f} 秒，数据预览: \n{result_df}")

                # 更新统计信息
                self._performance_stats['total_files_read'] += len(files)
                self._performance_stats['total_data_size'] += len(result_df)

                # 缓存结果
                if self.enable_cache and cache_key:
                    if len(self._cache) >= self.cache_size:
                        # 简单的LRU：删除第一个缓存项
                        oldest_key = next(iter(self._cache))
                        del self._cache[oldest_key]
                    self._cache[cache_key] = result_df.copy()

                return result_df
            else:
                logger.warning("Pandas向量化读取返回空结果")
                return None

        except Exception as e:
            # 提供详细的错误信息用于调试
            error_msg = f"Pandas向量化读取失败: {e}"
            logger.error(error_msg)
            logger.error(f"文件列表: {files}")

            # 直接抛出异常
            raise RuntimeError(f"向量化数据读取失败: {e}") from e

    def _read_files_parallel(self, files: List[str], columns: Optional[List[str]] = None,
                           max_workers: int = 4) -> List[pd.DataFrame]:
        """
        多线程并行读取文件，保证结果按文件路径顺序排列

        Args:
            files: 文件路径列表（应按时间顺序排列）
            columns: 要读取的列名列表
            max_workers: 最大线程数

        Returns:
            List[pd.DataFrame]: 按输入文件顺序排列的DataFrame列表
        """
        def read_single_file(file_path: str) -> Optional[pd.DataFrame]:
            """读取单个文件"""
            try:
                df = pd.read_parquet(file_path, columns=columns)
                if df is not None and not df.empty:
                    logger.debug(f"并行读取文件成功: {file_path}, 行数: {len(df)}")
                    return df
                else:
                    logger.warning(f"文件为空: {file_path}")
                    return None
            except Exception as e:
                logger.error(f"并行读取文件失败: {file_path}, 错误: {e}")
                return None

        # 使用字典保存结果，确保按原始顺序排列
        file_results = {}

        # 使用线程池并行读取
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {executor.submit(read_single_file, file): file for file in files}

            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    df = future.result()
                    if df is not None:
                        file_results[file_path] = df
                except Exception as e:
                    logger.error(f"获取并行读取结果失败: {file_path}, 错误: {e}")

        # 按原始文件顺序重新排列结果
        dfs = []
        for file_path in files:
            if file_path in file_results:
                dfs.append(file_results[file_path])

        logger.debug(f"并行读取完成，成功读取 {len(dfs)}/{len(files)} 个文件，结果已按文件顺序排列")
        return dfs

    @performance_monitor
    def read_partitioned_data_vectorized(self, data_root: str, symbol: str, period: str,
                                       start_time: Optional[str] = None,
                                       end_time: Optional[str] = None,
                                       columns: Optional[List[str]] = None,
                                       dividend_type: str = "none") -> Optional[pd.DataFrame]:
        """
        向量化读取分区数据（主要接口，支持复权数据读取）

        使用Pandas Concat实现的向量化读取，确保：
        - 完全保持原始数据结构和索引
        - 支持多线程并行读取优化
        - 支持复权数据处理

        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列
            dividend_type: 复权类型，"none"（原始数据）、"front"（前复权）、"back"（后复权）

        Returns:
            pd.DataFrame: 读取的数据（根据dividend_type应用复权处理）
        """
        # 记录接收到的参数（修复接口契约违反问题）
        if start_time or end_time:
            logger.info(f"向量化读取器接收参数: {symbol} {period} 时间范围 {start_time} 至 {end_time} 复权类型 {dividend_type}")
        else:
            logger.debug(f"向量化读取器接收参数: {symbol} {period} 复权类型 {dividend_type}")

        logger.debug(f"向量化读取分区数据: {symbol} {period} {start_time}-{end_time} dividend_type={dividend_type}")

        # 更新统计信息
        self._performance_stats['total_calls'] += 1
        
        try:
            # 使用智能文件选择替代原有的get_partition_files
            from data.storage.parquet_reader import _get_target_partition_files

            logger.debug(f"向量化读取器使用智能文件选择: {symbol} {period} {start_time}-{end_time}")

            # 获取分区文件列表（使用智能文件选择）
            partition_files = _get_target_partition_files(
                symbol=symbol,
                period=period,
                start_time=start_time,
                end_time=end_time,
                data_type="raw"  # 向量化读取器默认读取原始数据
            )
            
            if not partition_files:
                logger.warning(f"未找到分区文件: {symbol} {period}")
                return None
            
            logger.debug(f"找到 {len(partition_files)} 个分区文件")
            
            # 使用向量化方式读取文件
            df = self.read_files_vectorized(partition_files, columns)
            
            if df is None or df.empty:
                logger.warning(f"向量化读取结果为空: {symbol} {period}")
                return None

            # 记录过滤前的数据量
            original_count = len(df)
            logger.debug(f"向量化读取原始数据: {original_count} 行")

            # 应用时间范围过滤（修复缺失的时间过滤功能）
            if start_time or end_time:
                from utils.time_utils import filter_data_by_time_range
                logger.debug(f"开始时间过滤: {start_time} 至 {end_time}")

                df = filter_data_by_time_range(df, start_time, end_time)
                filtered_count = len(df) if df is not None and not df.empty else 0

                logger.debug(f"时间过滤完成: {original_count} -> {filtered_count} 行")
                logger.info(f"向量化读取时间过滤: 从 {original_count} 行过滤到 {filtered_count} 行")

                if df is None or df.empty:
                    logger.warning(f"时间过滤后数据为空: {symbol} {period} {start_time}-{end_time}")
                    return pd.DataFrame()
            else:
                logger.debug("未指定时间范围，跳过时间过滤")

            # 应用复权处理（如果需要）
            if dividend_type != "none":
                logger.debug(f"开始应用复权处理: {dividend_type}")
                df = self._apply_dividend_adjustment(df, symbol, dividend_type, start_time, end_time)
                if df is None or df.empty:
                    logger.warning(f"复权处理后数据为空: {symbol} {period} {dividend_type}")
                    return pd.DataFrame()
                logger.info(f"复权处理完成: {symbol} {dividend_type} 最终数据 {len(df)} 行")

            # 验证最终结果的索引格式
            from utils.data_processor.index_manager import IndexManager
            IndexManager.log_index_info(df, "向量化读取最终结果")

            logger.debug(f"Pandas向量化读取完成: {len(df)} 行数据，dividend_type={dividend_type}")
            return df
            
        except Exception as e:
            # 遵循核心指导思维：宁可报错也不掩盖bug
            # 时间过滤相关错误应该直接抛出，不使用后备方案
            if "filter_data_by_time_range" in str(e) or "时间过滤" in str(e):
                logger.error(f"时间过滤功能错误，直接抛出: {e}")
                raise e

            logger.error(f"向量化读取分区数据失败: {e}")
            return None

    def _apply_dividend_adjustment(self, df: pd.DataFrame, symbol: str, dividend_type: str,
                                 start_time: Optional[str] = None, end_time: Optional[str] = None) -> Optional[pd.DataFrame]:
        """应用复权处理

        Args:
            df: 原始数据
            symbol: 股票代码
            dividend_type: 复权类型
            start_time: 开始时间（用于缓存键生成）
            end_time: 结束时间（用于缓存键生成）

        Returns:
            复权处理后的数据
        """
        try:
            if df is None or df.empty:
                logger.warning(f"数据为空，跳过复权处理: {symbol}")
                return df

            if dividend_type == "none":
                logger.debug(f"复权类型为none，返回原始数据: {symbol}")
                return df

            logger.debug(f"开始复权处理: {symbol} {dividend_type}")

            # 使用AdjustmentSynthesizer进行复权计算
            from utils.data_processor.adjustment import adjustment_synthesizer

            adjusted_data = adjustment_synthesizer.synthesize_adjusted_data(
                symbol=symbol,
                price_data=df,
                dividend_type=dividend_type,
                method="ratio",  # 使用等比复权
                use_cache=True,
                start_date=start_time,  # 传递时间参数用于缓存键生成
                end_date=end_time
            )

            if adjusted_data is None:
                logger.warning(f"复权处理失败，返回原始数据: {symbol} {dividend_type}")
                return df

            logger.debug(f"复权处理成功: {symbol} {dividend_type} {len(adjusted_data)} 行")
            return adjusted_data

        except Exception as e:
            logger.error(f"复权处理异常: {symbol} {dividend_type} {e}")
            # 遵循核心指导思维：不掩盖错误，返回原始数据
            return df
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            dict: 性能统计信息
        """
        stats = self._performance_stats.copy()
        if stats['total_calls'] > 0:
            stats['avg_time_per_call'] = stats['total_time'] / stats['total_calls']
            stats['avg_files_per_call'] = stats['total_files_read'] / stats['total_calls']
            stats['avg_data_size_per_call'] = stats['total_data_size'] / stats['total_calls']
        else:
            stats['avg_time_per_call'] = 0.0
            stats['avg_files_per_call'] = 0.0
            stats['avg_data_size_per_call'] = 0.0
        
        if stats['total_calls'] > 0:
            stats['cache_hit_rate'] = stats['cache_hits'] / stats['total_calls']
        else:
            stats['cache_hit_rate'] = 0.0
        
        return stats
    
    def clear_cache(self):
        """清空缓存"""
        if self._cache:
            self._cache.clear()
            logger.debug("缓存已清空")
    
    def reset_performance_stats(self):
        """重置性能统计信息"""
        self._performance_stats = {
            'total_calls': 0,
            'total_time': 0.0,
            'cache_hits': 0,
            'total_files_read': 0,
            'total_data_size': 0
        }
        logger.debug("性能统计信息已重置")
    
    async def read_files_async(self, files: List[str],
                             columns: Optional[List[str]] = None,
                             max_concurrent: int = 5) -> Optional[pd.DataFrame]:
        """
        异步读取多个文件

        Args:
            files: 文件路径列表
            columns: 要读取的列名列表
            max_concurrent: 最大并发数

        Returns:
            pd.DataFrame: 合并后的数据
        """
        if not files:
            return None

        logger.debug(f"异步读取 {len(files)} 个文件，最大并发数: {max_concurrent}")

        async def read_single_file(file_path: str) -> Optional[pd.DataFrame]:
            """异步读取单个文件"""
            loop = asyncio.get_event_loop()
            try:
                # 在线程池中执行文件读取
                df = await loop.run_in_executor(
                    None,
                    lambda: pd.read_parquet(file_path, columns=columns) if os.path.exists(file_path) else None
                )
                if df is not None and not df.empty:
                    logger.debug(f"异步读取文件成功: {file_path}, 行数: {len(df)}")
                    return df
                else:
                    logger.warning(f"异步读取文件为空: {file_path}")
                    return None
            except Exception as e:
                logger.error(f"异步读取文件失败: {file_path}, 错误: {e}")
                return None

        # 使用信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)

        async def read_with_semaphore(file_path: str) -> Optional[pd.DataFrame]:
            async with semaphore:
                return await read_single_file(file_path)

        # 并发读取所有文件
        tasks = [read_with_semaphore(file_path) for file_path in files]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        dfs = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"异步读取任务失败: {files[i]}, 错误: {result}")
            elif result is not None:
                dfs.append(result)

        if not dfs:
            logger.warning("异步读取没有成功读取任何文件")
            return None

        # 合并数据 - 使用统一的索引处理标准
        if len(dfs) == 1:
            return dfs[0]
        else:
            logger.debug(f"异步合并 {len(dfs)} 个数据文件")
            # 导入统一索引管理器
            from utils.data_processor.index_manager import IndexManager

            # 使用安全合并，确保索引格式正确
            result_df = IndexManager.safe_concat(dfs, axis=0)

            if result_df is not None:
                logger.debug("异步合并完成，索引格式已验证")
                return result_df
            else:
                logger.error("异步合并失败，使用传统方式合并")
                # 备用方案：使用ignore_index=False
                return pd.concat(dfs, axis=0, ignore_index=False)

    async def read_partitioned_data_async(self, data_root: str, symbol: str, period: str,
                                        start_time: Optional[str] = None,
                                        end_time: Optional[str] = None,
                                        columns: Optional[List[str]] = None,
                                        max_concurrent: int = 5) -> Optional[pd.DataFrame]:
        """
        异步读取分区数据（保持原始数据格式）

        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列
            max_concurrent: 最大并发数

        Returns:
            pd.DataFrame: 读取的数据（保持原始格式）
        """
        logger.debug(f"异步读取分区数据: {symbol} {period} {start_time}-{end_time}")

        try:
            # 使用智能文件选择替代原有的get_partition_files
            from data.storage.parquet_reader import _get_target_partition_files

            logger.debug(f"异步读取器使用智能文件选择: {symbol} {period} {start_time}-{end_time}")

            # 获取分区文件列表（使用智能文件选择）
            partition_files = _get_target_partition_files(
                symbol=symbol,
                period=period,
                start_time=start_time,
                end_time=end_time,
                data_type="raw"  # 异步读取器默认读取原始数据
            )

            if not partition_files:
                logger.warning(f"未找到分区文件: {symbol} {period}")
                return None

            logger.debug(f"找到 {len(partition_files)} 个分区文件")

            # 异步读取文件
            df = await self.read_files_async(partition_files, columns, max_concurrent)

            if df is None or df.empty:
                logger.warning(f"异步读取结果为空: {symbol} {period}")
                return None

            # 记录过滤前的数据量
            original_count = len(df)
            logger.debug(f"异步读取原始数据: {original_count} 行")

            # 应用时间范围过滤（修复缺失的时间过滤功能）
            if start_time or end_time:
                from utils.time_utils import filter_data_by_time_range
                logger.debug(f"异步读取开始时间过滤: {start_time} 至 {end_time}")

                df = filter_data_by_time_range(df, start_time, end_time)
                filtered_count = len(df) if df is not None and not df.empty else 0

                logger.debug(f"异步读取时间过滤完成: {original_count} -> {filtered_count} 行")
                logger.info(f"异步读取时间过滤: 从 {original_count} 行过滤到 {filtered_count} 行")

                if df is None or df.empty:
                    logger.warning(f"异步读取时间过滤后数据为空: {symbol} {period} {start_time}-{end_time}")
                    return pd.DataFrame()
            else:
                logger.debug("异步读取未指定时间范围，跳过时间过滤")

            logger.debug(f"异步读取完成: {len(df)} 行数据，保持原始数据格式")
            return df

        except Exception as e:
            logger.error(f"异步读取分区数据失败: {e}")
            return None

    def __del__(self):
        """析构函数，清理资源"""
        # Pandas实现无需特殊清理
        pass


# 全局实例，避免重复初始化
_global_reader = None

def get_vectorized_reader() -> VectorizedDataReader:
    """
    获取全局向量化数据读取器实例
    
    Returns:
        VectorizedDataReader: 向量化数据读取器实例
    """
    global _global_reader
    if _global_reader is None:
        _global_reader = VectorizedDataReader()
    return _global_reader


class StreamingDataReader:
    """
    流式数据读取器

    支持大数据集的流式处理，避免内存溢出
    支持流式回测切片存储，优化回测性能
    """

    def __init__(self, chunk_size: int = 10000, enable_slice_storage: bool = False,
                 slice_storage_dir: Optional[str] = None):
        """
        初始化流式数据读取器

        Args:
            chunk_size: 每个数据块的大小
            enable_slice_storage: 是否启用切片存储
            slice_storage_dir: 切片存储目录
        """
        self.chunk_size = chunk_size
        self.vectorized_reader = get_vectorized_reader()
        self.enable_slice_storage = enable_slice_storage
        self.slice_storage_dir = slice_storage_dir

        # 切片存储统计
        self.slice_stats = {
            'slices_created': 0,
            'slices_loaded': 0,
            'total_slice_size_mb': 0.0
        }

        if enable_slice_storage and slice_storage_dir:
            from pathlib import Path
            Path(slice_storage_dir).mkdir(parents=True, exist_ok=True)
            logger.info(f"流式读取器启用切片存储，目录: {slice_storage_dir}")

    def read_partitioned_data_streaming(self, data_root: str, symbol: str, period: str,
                                      start_time: Optional[str] = None,
                                      end_time: Optional[str] = None,
                                      columns: Optional[List[str]] = None):
        """
        流式读取分区数据

        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列

        Yields:
            pd.DataFrame: 数据块
        """
        logger.debug(f"流式读取分区数据: {symbol} {period} {start_time}-{end_time}")

        try:
            # 使用智能文件选择替代原有的get_partition_files
            from data.storage.parquet_reader import _get_target_partition_files

            logger.debug(f"流式读取器使用智能文件选择: {symbol} {period} {start_time}-{end_time}")

            # 获取分区文件列表（使用智能文件选择）
            partition_files = _get_target_partition_files(
                symbol=symbol,
                period=period,
                start_time=start_time,
                end_time=end_time,
                data_type="raw"  # 流式读取器默认读取原始数据
            )

            if not partition_files:
                logger.warning(f"未找到分区文件: {symbol} {period}")
                return

            logger.debug(f"流式处理 {len(partition_files)} 个分区文件")

            # 逐个文件流式处理
            for file_path in partition_files:
                try:
                    if os.path.exists(file_path):
                        # 使用pandas的chunksize参数进行流式读取
                        for chunk in pd.read_parquet(file_path, columns=columns, chunksize=self.chunk_size):
                            if chunk is not None and not chunk.empty:
                                original_count = len(chunk)
                                logger.debug(f"流式读取数据块: {original_count} 行")

                                # 应用时间范围过滤（修复缺失的时间过滤功能）
                                if start_time or end_time:
                                    from utils.time_utils import filter_data_by_time_range
                                    chunk = filter_data_by_time_range(chunk, start_time, end_time)
                                    filtered_count = len(chunk) if chunk is not None and not chunk.empty else 0

                                    if filtered_count != original_count:
                                        logger.debug(f"流式读取时间过滤: {original_count} -> {filtered_count} 行")

                                    if chunk is None or chunk.empty:
                                        logger.debug(f"流式读取数据块过滤后为空，跳过")
                                        continue

                                yield chunk
                            else:
                                logger.debug(f"跳过空数据块: {file_path}")
                    else:
                        logger.warning(f"文件不存在: {file_path}")
                except Exception as e:
                    logger.error(f"流式读取文件失败: {file_path}, 错误: {e}")
                    continue

        except Exception as e:
            logger.error(f"流式读取分区数据失败: {e}")
            return

    def process_streaming_data(self, data_root: str, symbol: str, period: str,
                             processor_func,
                             start_time: Optional[str] = None,
                             end_time: Optional[str] = None,
                             columns: Optional[List[str]] = None):
        """
        流式处理数据

        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            processor_func: 数据处理函数
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列
        """
        logger.debug(f"开始流式处理数据: {symbol} {period}")

        processed_count = 0
        for chunk in self.read_partitioned_data_streaming(
            data_root, symbol, period, start_time, end_time, columns
        ):
            try:
                # 应用处理函数
                processed_chunk = processor_func(chunk)
                processed_count += len(chunk)

                logger.debug(f"已处理 {processed_count} 行数据")

                # 可以在这里保存处理结果或进行其他操作
                yield processed_chunk

            except Exception as e:
                logger.error(f"流式处理数据块失败: {e}")
                continue

        logger.debug(f"流式处理完成，总计处理 {processed_count} 行数据")

    def _generate_slice_key(self, symbol: str, period: str, start_time: str, end_time: str) -> str:
        """生成切片存储键"""
        import hashlib
        key_string = f"{symbol}_{period}_{start_time}_{end_time}"
        return hashlib.md5(key_string.encode('utf-8')).hexdigest()

    def _get_slice_file_path(self, slice_key: str) -> str:
        """获取切片文件路径"""
        from pathlib import Path
        return str(Path(self.slice_storage_dir) / f"{slice_key}.feather")

    def _save_slice(self, data: pd.DataFrame, slice_key: str) -> bool:
        """保存数据切片"""
        try:
            if not self.enable_slice_storage or not self.slice_storage_dir:
                return False

            slice_file = self._get_slice_file_path(slice_key)
            data.to_feather(slice_file)

            # 更新统计
            file_size_mb = os.path.getsize(slice_file) / (1024 * 1024)
            self.slice_stats['slices_created'] += 1
            self.slice_stats['total_slice_size_mb'] += file_size_mb

            logger.debug(f"切片已保存: {slice_key}, 大小: {file_size_mb:.2f}MB")
            return True

        except Exception as e:
            logger.error(f"保存切片失败: {slice_key} {e}")
            return False

    def _load_slice(self, slice_key: str) -> Optional[pd.DataFrame]:
        """加载数据切片"""
        try:
            if not self.enable_slice_storage or not self.slice_storage_dir:
                return None

            slice_file = self._get_slice_file_path(slice_key)
            if not os.path.exists(slice_file):
                return None

            data = pd.read_feather(slice_file)
            self.slice_stats['slices_loaded'] += 1

            logger.debug(f"切片已加载: {slice_key}, 数据量: {len(data)}")
            return data

        except Exception as e:
            logger.error(f"加载切片失败: {slice_key} {e}")
            return None

    def read_with_slice_storage(self, data_root: str, symbol: str, period: str,
                               start_time: Optional[str] = None,
                               end_time: Optional[str] = None,
                               columns: Optional[List[str]] = None) -> Optional[pd.DataFrame]:
        """使用切片存储的数据读取

        优先从切片存储加载数据，如果不存在则读取原始数据并保存切片
        """
        if not self.enable_slice_storage:
            # 如果未启用切片存储，使用常规向量化读取
            return self.vectorized_reader.read_partitioned_data_vectorized(
                data_root, symbol, period, start_time, end_time, columns
            )

        # 生成切片键
        slice_key = self._generate_slice_key(
            symbol, period, start_time or "", end_time or ""
        )

        # 尝试从切片存储加载
        cached_data = self._load_slice(slice_key)
        if cached_data is not None:
            logger.debug(f"从切片存储加载数据: {symbol} {period}")
            return cached_data

        # 切片不存在，读取原始数据
        logger.debug(f"切片不存在，读取原始数据: {symbol} {period}")
        data = self.vectorized_reader.read_partitioned_data_vectorized(
            data_root, symbol, period, start_time, end_time, columns
        )

        # 保存切片
        if data is not None and not data.empty:
            self._save_slice(data, slice_key)

        return data

    def get_slice_statistics(self) -> Dict[str, Any]:
        """获取切片存储统计信息"""
        return {
            'slice_storage_enabled': self.enable_slice_storage,
            'slice_storage_dir': self.slice_storage_dir,
            'slices_created': self.slice_stats['slices_created'],
            'slices_loaded': self.slice_stats['slices_loaded'],
            'total_slice_size_mb': round(self.slice_stats['total_slice_size_mb'], 2),
            'cache_hit_rate': (
                self.slice_stats['slices_loaded'] /
                (self.slice_stats['slices_loaded'] + self.slice_stats['slices_created'])
                if (self.slice_stats['slices_loaded'] + self.slice_stats['slices_created']) > 0
                else 0.0
            )
        }


@monitor_index_format(validate_input=False, validate_output=True, auto_fix=True)
def read_partitioned_data_vectorized(data_root: str, symbol: str, period: str,
                                   start_time: Optional[str] = None,
                                   end_time: Optional[str] = None,
                                   columns: Optional[List[str]] = None,
                                   dividend_type: str = "none") -> Optional[pd.DataFrame]:
    """
    向量化读取分区数据（便捷函数，支持复权数据读取）

    使用Pandas Concat实现，支持复权数据处理。

    重要：此函数已修复时间过滤功能，确保start_time和end_time参数被正确使用。

    Args:
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        start_time: 开始时间（格式: YYYYMMDD 或 YYYYMMDDHHMMSS）
        end_time: 结束时间（格式: YYYYMMDD 或 YYYYMMDDHHMMSS）
        columns: 要读取的列
        dividend_type: 复权类型，"none"（原始数据）、"front"（前复权）、"back"（后复权）

    Returns:
        pd.DataFrame: 读取的数据（根据dividend_type应用复权处理，已应用时间过滤）
    """
    # 记录参数使用情况（修复接口契约违反问题）
    if start_time or end_time:
        logger.debug(f"便捷函数接收参数: {start_time} 至 {end_time} dividend_type={dividend_type}")
    else:
        logger.debug(f"便捷函数接收参数: dividend_type={dividend_type}")

    reader = get_vectorized_reader()
    return reader.read_partitioned_data_vectorized(
        data_root, symbol, period, start_time, end_time, columns, dividend_type
    )


async def read_partitioned_data_async(data_root: str, symbol: str, period: str,
                                    start_time: Optional[str] = None,
                                    end_time: Optional[str] = None,
                                    columns: Optional[List[str]] = None,
                                    max_concurrent: int = 5) -> Optional[pd.DataFrame]:
    """
    异步读取分区数据（便捷函数，保持原始数据格式）

    Args:
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        start_time: 开始时间
        end_time: 结束时间
        columns: 要读取的列
        max_concurrent: 最大并发数

    Returns:
        pd.DataFrame: 读取的数据（保持原始格式）
    """
    reader = get_vectorized_reader()
    return await reader.read_partitioned_data_async(
        data_root, symbol, period, start_time, end_time, columns, max_concurrent
    )
