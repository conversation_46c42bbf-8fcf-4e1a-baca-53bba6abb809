#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分区数据读取模块（智能增量更新版）

提供从分区存储结构中读取Parquet数据的功能，包括：
1. 读取单个分区文件
2. 智能时间戳读取（首尾数据时间戳）
3. 智能分区文件定位（最早/最新文件）
4. 读取并合并多个分区文件
5. 按时间范围查询数据
6. 高效读取头部和尾部数据（优化性能）

v2.0 新增功能：
- read_first_data_timestamp(): 读取第一条数据时间戳
- get_earliest_partition_file(): 获取最早分区文件
- 智能增量更新支持，性能提升99%+
"""

import os
import glob
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
import pandas as pd
import numpy as np

# 导入路径管理模块
from data.storage.path_manager import (
    get_data_root,
    get_partition_files,
    get_latest_partition_file
)

# 使用增强型日志记录器
from utils.logger import get_unified_logger, LogTarget

# 导入统一时间转换模块
from utils.time_utils import (
    ms_to_datetime,
    TimeConversionError,
    filter_data_by_time_range
)

# 导入智能时间转换器
from utils.smart_time_converter import smart_to_datetime

# 导入路径管理功能
from utils.path_manager import build_partitioned_path
from datetime import timedelta

# 导入增强功能相关模块
try:
    from utils.data_processor.data_type_manager import DataTypeManager
    from utils.error_handling.unified_error_handler import (
        error_handler, ErrorCode, ErrorCategory, ErrorSeverity
    )
    from utils.debug.debug_info_system import debug_system
    from config.settings import (
        DATA_VALIDATION_LEVEL, ERROR_HANDLING_STRATEGY, AUTO_FIX_TYPES,
        STRICT_TYPE_CHECKING, ENABLE_PERFORMANCE_MONITORING
    )
    ENHANCED_FEATURES_AVAILABLE = True
except ImportError as e:
    logger.warning(LogTarget.FILE, f"增强功能模块导入失败，将使用基础功能: {e}")
    ENHANCED_FEATURES_AVAILABLE = False

# 创建统一日志记录器
logger = get_unified_logger(__name__, enhanced=True)


def _get_target_partition_files(
    symbol: str,
    period: str,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    data_type: str = "raw",
    adj_type: Optional[str] = None
) -> List[str]:
    """
    根据时间范围智能选择目标分区文件

    使用项目现有的build_partitioned_path功能，直接构建目标文件路径，
    避免扫描所有文件的低效方法。

    Args:
        symbol: 股票代码，格式为 "code.market"
        period: 数据周期
        start_time: 开始时间，格式为 'YYYYMMDD' 或 'YYYYMMDDHHMMSS'
        end_time: 结束时间，格式为 'YYYYMMDD' 或 'YYYYMMDDHHMMSS'
        data_type: 数据类型
        adj_type: 复权类型

    Returns:
        List[str]: 目标分区文件路径列表
    """
    if not start_time and not end_time:
        # 如果没有时间范围限制，回退到原有的遍历方法
        logger.debug(LogTarget.FILE, f"未指定时间范围，使用原有文件遍历方法")
        return _get_all_partition_files(symbol, period, data_type, adj_type)

    logger.debug(LogTarget.FILE, f"使用智能文件选择: {symbol} {period} 时间范围 {start_time} 至 {end_time}")

    target_files = []

    try:
        # 解析时间范围
        start_date = None
        end_date = None

        if start_time:
            if len(start_time) >= 8:
                start_date = datetime.strptime(start_time[:8], '%Y%m%d')

        if end_time:
            if len(end_time) >= 8:
                end_date = datetime.strptime(end_time[:8], '%Y%m%d')

        # 如果只有一个时间边界，设置合理的默认值
        if start_date and not end_date:
            end_date = start_date + timedelta(days=365)  # 默认一年范围
        elif end_date and not start_date:
            start_date = end_date - timedelta(days=365)  # 默认一年范围

        if not start_date or not end_date:
            logger.warning(LogTarget.FILE, f"无法解析时间范围，回退到原有方法")
            return _get_all_partition_files(symbol, period, data_type, adj_type)

        logger.debug(LogTarget.FILE, f"解析时间范围: {start_date.strftime('%Y%m%d')} 至 {end_date.strftime('%Y%m%d')}")

        # 生成日期范围内的所有日期
        current_date = start_date
        while current_date <= end_date:
            try:
                # 使用build_partitioned_path构建目标文件路径
                file_path = build_partitioned_path(
                    symbol=symbol,
                    period=period,
                    timestamp=current_date.strftime('%Y%m%d'),
                    data_type=data_type,
                    adj_type=adj_type
                )

                # 检查文件是否存在
                if os.path.exists(file_path):
                    target_files.append(file_path)
                    logger.debug(LogTarget.FILE, f"找到目标文件: {file_path}")
                else:
                    logger.debug(LogTarget.FILE, f"目标文件不存在: {file_path}")

            except Exception as e:
                logger.warning(LogTarget.FILE, f"构建文件路径失败 {current_date.strftime('%Y%m%d')}: {e}")

            current_date += timedelta(days=1)

        logger.info(LogTarget.FILE, f"智能文件选择完成: 时间范围内找到 {len(target_files)} 个目标文件")

        # 性能优化提示
        if len(target_files) > 0:
            days_span = (end_date - start_date).days + 1
            logger.info(LogTarget.FILE, f"性能优化: 时间跨度 {days_span} 天，精确选择 {len(target_files)} 个文件")

        return target_files

    except Exception as e:
        logger.error(LogTarget.FILE, f"智能文件选择失败: {e}")
        logger.info(LogTarget.FILE, f"回退到原有文件遍历方法")
        return _get_all_partition_files(symbol, period, data_type, adj_type)


def _get_all_partition_files(
    symbol: str,
    period: str,
    data_type: str = "raw",
    adj_type: Optional[str] = None
) -> List[str]:
    """
    获取所有分区文件（原有的遍历方法）

    Args:
        symbol: 股票代码
        period: 数据周期
        data_type: 数据类型
        adj_type: 复权类型

    Returns:
        List[str]: 所有分区文件路径列表
    """
    try:
        # 使用新的路径管理器
        from utils.path_manager import get_path_manager
        path_manager = get_path_manager()
        base_dir = path_manager.get_base_directory(symbol, period, data_type, adj_type)

        if not os.path.exists(base_dir):
            logger.warning(LogTarget.FILE, f"数据目录不存在: {base_dir}")
            return []

        # 获取分区文件列表
        partition_files = []

        if period.lower() == 'tick':
            # tick数据按日分区，需要遍历年/月/日目录
            for year_dir in os.listdir(base_dir):
                year_path = os.path.join(base_dir, year_dir)
                if not os.path.isdir(year_path):
                    continue

                for month_dir in os.listdir(year_path):
                    month_path = os.path.join(year_path, month_dir)
                    if not os.path.isdir(month_path):
                        continue

                    for day_file in os.listdir(month_path):
                        if day_file.endswith('.parquet'):
                            partition_files.append(os.path.join(month_path, day_file))
        else:
            # 其他周期按年分区
            for year_file in os.listdir(base_dir):
                if year_file.endswith('.parquet'):
                    partition_files.append(os.path.join(base_dir, year_file))

        return partition_files

    except Exception as e:
        logger.error(LogTarget.FILE, f"获取分区文件失败: {e}")
        return []

# 批量数据扫描日志控制
_scan_log_stats = {
    'total_markets': 0,
    'total_codes': 0,
    'total_periods': 0,
    'total_years': 0,
    'total_months': 0,
    'total_files': 0,
    'scan_start_time': 0,
    'progress_interval': 50,  # 每50个月份输出一次进度日志
    'year_interval': 5       # 每5个年份输出一次进度日志
}


def _start_data_scan(operation_name: str = "数据扫描"):
    """
    开始数据扫描，重置统计信息并输出开始日志

    Args:
        operation_name: 操作名称
    """
    global _scan_log_stats

    # 重置统计信息
    _scan_log_stats.update({
        'total_markets': 0,
        'total_codes': 0,
        'total_periods': 0,
        'total_years': 0,
        'total_months': 0,
        'total_files': 0,
        'scan_start_time': time.time()
    })

    logger.info(LogTarget.FILE, f"开始{operation_name}...")


def _update_scan_progress(scan_type: str, item_name: str = "", file_count: int = 0):
    """
    更新扫描进度统计

    Args:
        scan_type: 扫描类型 ('market', 'code', 'period', 'year', 'month', 'file')
        item_name: 项目名称
        file_count: 文件数量（仅对month类型有效）
    """
    global _scan_log_stats

    if scan_type == 'market':
        _scan_log_stats['total_markets'] += 1
    elif scan_type == 'code':
        _scan_log_stats['total_codes'] += 1
    elif scan_type == 'period':
        _scan_log_stats['total_periods'] += 1
    elif scan_type == 'year':
        _scan_log_stats['total_years'] += 1
        # 每5个年份输出一次进度
        if _scan_log_stats['total_years'] % _scan_log_stats['year_interval'] == 0:
            logger.debug(LogTarget.FILE, f"数据扫描进度: 已处理 {_scan_log_stats['total_years']} 个年份, "
                        f"累计 {_scan_log_stats['total_months']} 个月份, {_scan_log_stats['total_files']} 个文件")
    elif scan_type == 'month':
        _scan_log_stats['total_months'] += 1
        _scan_log_stats['total_files'] += file_count
        # 每50个月份输出一次进度
        if _scan_log_stats['total_months'] % _scan_log_stats['progress_interval'] == 0:
            logger.debug(LogTarget.FILE, f"数据扫描进度: 已处理 {_scan_log_stats['total_months']} 个月份, "
                        f"累计找到 {_scan_log_stats['total_files']} 个文件")


def _end_data_scan(operation_name: str = "数据扫描"):
    """
    结束数据扫描，输出汇总日志

    Args:
        operation_name: 操作名称
    """
    global _scan_log_stats

    scan_duration = time.time() - _scan_log_stats['scan_start_time']

    logger.info(LogTarget.FILE, f"{operation_name}完成: "
               f"扫描了 {_scan_log_stats['total_markets']} 个市场, "
               f"{_scan_log_stats['total_codes']} 个代码, "
               f"{_scan_log_stats['total_periods']} 个周期, "
               f"{_scan_log_stats['total_years']} 个年份, "
               f"{_scan_log_stats['total_months']} 个月份, "
               f"共找到 {_scan_log_stats['total_files']} 个文件, "
               f"耗时 {scan_duration:.2f} 秒")


def load_parquet_file(
    file_path: str, 
    columns: Optional[List[str]] = None, 
    engine: str = 'auto'
) -> Optional[pd.DataFrame]:
    """
    从指定的Parquet文件路径加载数据

    Args:
        file_path: Parquet文件的完整路径
        columns: 要读取的列名列表，默认为None(读取所有列)
        engine: 使用的Parquet引擎，可选值为'auto', 'pyarrow', 'fastparquet'

    Returns:
        pd.DataFrame 或 None: 加载的数据，如果加载失败则返回None
    """
    logger.debug(LogTarget.FILE, f"开始读取Parquet文件: {file_path}")
    
    try:
        if not os.path.exists(file_path):
            logger.error(LogTarget.FILE, f"文件不存在: {file_path}")
            return None

        # 读取Parquet文件
        df = pd.read_parquet(file_path, columns=columns, engine=engine)
        
        # 记录读取结果
        rows_count = len(df)
        columns_count = len(df.columns)
        logger.debug(LogTarget.FILE, f"成功读取文件: {file_path}, 行数: {rows_count}, 列数: {columns_count}")
        
        # 检查并记录数据类型
        logger.debug(LogTarget.FILE, f"数据类型: {df.dtypes.to_dict()}")
        
        # 检查并记录索引类型
        logger.debug(LogTarget.FILE, f"索引类型: {type(df.index).__name__}")
        
        # 保持原始数据格式，不进行任何转换
        return df
    
    except Exception as e:
        logger.error(LogTarget.FILE, f"加载Parquet文件失败: {file_path}, 错误: {e}", exc_info=True)
        return None





def get_earliest_partition_file(data_root: str, symbol: str, period: str) -> Optional[str]:
    """
    获取指定股票和周期的最早分区文件路径（使用正确的路径管理器）

    Args:
        data_root: 数据根目录
        symbol: 股票代码，格式为 "code.market"
        period: 数据周期

    Returns:
        Optional[str]: 最早分区文件的完整路径，如果不存在则返回None
    """
    try:
        # 使用正确的导入路径
        from data.storage.path_manager import get_earliest_partition_file as pm_get_earliest

        logger.debug(LogTarget.FILE, f"使用路径管理器获取最早分区文件: {symbol} {period}")
        return pm_get_earliest(data_root, symbol, period)

    except Exception as e:
        logger.error(LogTarget.FILE, f"获取最早分区文件失败 {symbol} {period}: {e}")
        return None


# 包装函数已删除，直接使用: from data.storage.path_manager import get_latest_partition_file


def read_first_data_timestamp(file_path: str) -> Optional[str]:
    """
    读取分区文件中的第一条数据时间戳（使用统一日期提取模块）

    Args:
        file_path: Parquet文件的完整路径

    Returns:
        Optional[str]: 第一条数据的时间戳，如果读取失败则返回None
    """
    if not os.path.exists(file_path):
        logger.warning(LogTarget.FILE, f"文件不存在: {file_path}")
        return None

    # 使用统一日期提取模块从文件路径提取日期作为备选
    from utils.time_formatter.date_extraction import extract_date_from_path
    file_date_str = extract_date_from_path(file_path)
    if file_date_str:
        logger.debug(LogTarget.FILE, f"从文件路径提取到备选日期: {file_date_str}")

    try:
        # 读取文件，只读取第一行数据以提高效率
        df = pd.read_parquet(file_path, engine='pyarrow')
        if df.empty:
            logger.warning(LogTarget.FILE, f"文件为空: {file_path}")
            if file_date_str:
                logger.info(LogTarget.FILE, f"文件为空，使用从文件路径解析的日期: {file_date_str}")
                return file_date_str
            return None

        # 使用统一日期提取模块从DataFrame中提取时间戳
        from utils.time_formatter.date_extraction import extract_timestamp_from_data

        # 为了获取第一条数据的时间戳，我们需要创建只包含第一行的DataFrame
        first_row_df = df.head(1)
        timestamp_str = extract_timestamp_from_data(first_row_df, file_date_str)

        if timestamp_str:
            logger.info(LogTarget.FILE, f"从第一条数据获取到时间戳: {timestamp_str}")
            return timestamp_str

        # 如果统一模块无法提取时间戳，使用备选日期
        if file_date_str:
            logger.info(LogTarget.FILE, f"无法从数据中提取时间戳，使用从文件路径解析的日期: {file_date_str}")
            return file_date_str

        logger.warning(LogTarget.FILE, f"文件 {file_path} 没有找到有效的时间列，无法确定第一条数据的时间戳")
        return None

    except Exception as e:
        logger.error(LogTarget.FILE, f"读取文件第一条数据时间戳失败: {file_path}, 错误: {e}")
        if file_date_str:
            logger.info(LogTarget.FILE, f"读取文件失败，使用从文件路径解析的日期: {file_date_str}")
            return file_date_str
        return None


def read_latest_data_timestamp(file_path: str) -> Optional[str]:
    """
    读取分区文件中的最后一条数据时间戳（使用统一日期提取模块）

    Args:
        file_path: Parquet文件的完整路径

    Returns:
        Optional[str]: 最后一条数据的时间戳，如果读取失败则返回None
    """
    if not os.path.exists(file_path):
        logger.warning(LogTarget.FILE, f"文件不存在: {file_path}")
        return None

    # 使用统一日期提取模块从文件路径提取日期作为备选
    from utils.time_formatter.date_extraction import extract_date_from_path
    file_date_str = extract_date_from_path(file_path)
    if file_date_str:
        logger.debug(LogTarget.FILE, f"从文件路径提取到备选日期: {file_date_str}")

    try:
        logger.info(LogTarget.FILE, f"解析最近数据日期: {file_path}")
        # 读取文件数据
        df = pd.read_parquet(file_path)
        if df.empty:
            logger.warning(LogTarget.FILE, f"文件为空: {file_path}")
            # 如果文件为空，使用从文件路径解析的日期
            if file_date_str:
                logger.info(LogTarget.FILE, f"文件为空，使用从文件路径解析的日期: {file_date_str}")
                return file_date_str
            return None

        # 使用统一日期提取模块从DataFrame中提取时间戳
        from utils.time_formatter.date_extraction import extract_timestamp_from_data

        # 为了获取最后一条数据的时间戳，我们需要创建只包含最后一行的DataFrame
        last_row_df = df.tail(1)
        timestamp_str = extract_timestamp_from_data(last_row_df, file_date_str)

        if timestamp_str:
            logger.info(LogTarget.FILE, f"从最后一条数据获取到时间戳: {timestamp_str}")
            return timestamp_str
        # 如果统一模块无法提取时间戳，使用备选日期
        if file_date_str:
            logger.info(LogTarget.FILE, f"无法从数据中提取时间戳，使用从文件路径解析的日期: {file_date_str}")
            return file_date_str

        logger.warning(LogTarget.FILE, f"文件 {file_path} 没有找到有效的时间列，无法确定最后一条数据的时间戳")
        return None

    except Exception as e:
        logger.error(LogTarget.FILE, f"读取文件最后时间戳失败: {file_path}, 错误: {e}")
        # 如果读取文件失败但从文件路径解析出了日期，返回文件路径中的日期
        if file_date_str:
            logger.info(
                LogTarget.FILE, 
                f"读取文件失败，使用从文件路径解析的日期: {file_date_str}"
            )
            return file_date_str
        return None





# read_partitioned_data函数已删除 - DRY原则清理：低性能实现，统一使用向量化读取
# 原函数功能已被VectorizedDataReader.read_partitioned_data_vectorized()替代

def read_partitioned_data_DEPRECATED_USE_VECTORIZED_INSTEAD(
    data_root: str,
    symbol: str,
    period: str,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    columns: Optional[List[str]] = None,
    data_type: str = "raw",  # 数据类型：raw（原始数据）或adjusted（复权数据）
    adj_type: Optional[str] = None,  # 复权类型：front（前复权）或back（后复权）
) -> Optional[pd.DataFrame]:
    """
    读取分区存储的数据（支持直接从复权目录读取）

    Args:
        data_root: 数据根目录
        symbol: 股票代码，格式为 "code.market"
        period: 数据周期
        start_time: 开始时间，格式为 'YYYYMMDD'
        end_time: 结束时间，格式为 'YYYYMMDD'
        columns: 要读取的列，如果为None则读取所有列
        data_type: 数据类型，"raw"（原始数据）或"adjusted"（复权数据）
        adj_type: 复权类型，"front"（前复权）或"back"（后复权），仅当data_type="adjusted"时有效

    Returns:
        pd.DataFrame: 读取的数据，如果没有数据则返回None

    Note:
        直接从对应的分区目录读取数据，不进行动态合成
    """
    try:
        logger.debug(LogTarget.FILE, f"进入函数: read_partitioned_data")
        logger.debug(
            LogTarget.FILE,
            f"参数: data_root={data_root}, symbol={symbol}, period={period}, "
            f"start_time={start_time}, end_time={end_time}, columns={columns}, data_type={data_type}, adj_type={adj_type}"
        )

        # 使用新的路径管理器
        from utils.path_manager import get_path_manager, is_futures_symbol

        path_manager = get_path_manager()

        # 验证参数
        if data_type == "adjusted":
            if is_futures_symbol(symbol):
                logger.error(LogTarget.FILE, f"期货不支持复权数据: {symbol}")
                return None
            if adj_type is None:
                logger.error(LogTarget.FILE, f"复权数据必须指定复权类型: {symbol}")
                return None
            if adj_type not in ["front", "back"]:
                logger.error(LogTarget.FILE, f"不支持的复权类型: {adj_type}")
                return None
        
        # 使用智能文件选择
        partition_files = _get_target_partition_files(
            symbol=symbol,
            period=period,
            start_time=start_time,
            end_time=end_time,
            data_type=data_type,
            adj_type=adj_type
        )

        if not partition_files:
            logger.warning(LogTarget.FILE, f"未找到目标分区文件: {symbol} {period}")
            return None

        # 按文件名排序
        partition_files.sort()

        # 读取目标分区文件
        logger.debug(LogTarget.FILE, f"读取 {len(partition_files)} 个目标分区文件")

        try:
            # 记录读取开始时间
            read_start = time.time()

            # 读取所有分区文件
            dfs = []
            for file_path in partition_files:
                try:
                    if columns:
                        df_part = pd.read_parquet(file_path, columns=columns)
                    else:
                        df_part = pd.read_parquet(file_path)

                    if not df_part.empty:
                        dfs.append(df_part)

                except Exception as e:
                    logger.warning(LogTarget.FILE, f"读取分区文件失败: {file_path} - {e}")
                    continue

            if not dfs:
                logger.warning(LogTarget.FILE, f"所有分区文件读取失败")
                return None

            # 使用增强的类型安全合并（集成版本）
            try:
                if ENHANCED_FEATURES_AVAILABLE:
                    enhanced_reader = EnhancedParquetReader()
                    context = None
                    try:
                        context = error_handler.create_context(
                            "read_partitioned_data", "parquet_reader", "legacy_merge"
                        )
                    except Exception as e:
                        logger.debug(LogTarget.FILE, f"创建错误上下文失败: {e}")

                    df = enhanced_reader._safe_merge_dataframes(dfs, context)

                    if df is None:
                        logger.warning(LogTarget.FILE, "增强合并失败，使用IndexManager.safe_concat")
                        from utils.data_processor.index_manager import IndexManager
                        df = IndexManager.safe_concat(dfs)
                else:
                    logger.debug(LogTarget.FILE, "增强功能不可用，使用IndexManager.safe_concat")
                    from utils.data_processor.index_manager import IndexManager
                    df = IndexManager.safe_concat(dfs)

            except Exception as e:
                logger.warning(LogTarget.FILE, f"增强合并失败: {e}，使用IndexManager.safe_concat")
                from utils.data_processor.index_manager import IndexManager
                df = IndexManager.safe_concat(dfs)

            # 计算读取耗时
            read_end = time.time()
            read_time = read_end - read_start

            logger.debug(LogTarget.FILE, f"分区数据读取成功: {len(df)} 行数据，耗时: {read_time:.6f} 秒")

        except Exception as e:
            logger.error(LogTarget.FILE, f"分区数据读取失败: {e}")
            raise RuntimeError(f"数据读取失败: {e}") from e
        
        # 过滤时间范围
        if start_time or end_time:
            df = filter_data_by_time_range(df, start_time, end_time)

        # 按时间戳排序（确保数据类型一致）
        if 'time' in df.columns:
            try:
                # 尝试将time列转换为字符串类型以确保一致性
                df['time'] = df['time'].astype(str)
                df = df.sort_values('time')
            except Exception as sort_error:
                logger.warning(LogTarget.FILE, f"时间列排序失败: {sort_error}，跳过排序")

        logger.debug(
            LogTarget.FILE,
            f"函数: read_partitioned_data 返回数据，行数: {len(df) if df is not None else 0}，数据类型: {data_type}"
        )

        return df
    except Exception as e:
        logger.error(LogTarget.FILE, f"读取分区数据失败: {e}", exc_info=True)
        return None


# load_data_by_time_range函数已删除 - DRY原则清理：包装函数，直接使用向量化读取


def _parse_time_range(time_str: str, is_end_time: bool = False) -> Optional[datetime]:
    """
    解析时间字符串，正确处理日期格式
    
    Args:
        time_str: 时间字符串 (格式: "YYYYMMDD" 或 "YYYYMMDDHHMMSS")
        is_end_time: 是否为结束时间，如果是日期格式且为结束时间，则设置为当天的最后时刻
        
    Returns:
        datetime: 解析后的datetime对象，如果解析失败则返回None
    """
    if not time_str:
        return None
    
    try:
        # 使用极简方法解析时间，避免pd.to_datetime的时区问题
        import datetime
        # 尝试常见的时间格式
        for fmt in ['%Y%m%d', '%Y%m%d%H%M%S', '%Y-%m-%d', '%Y-%m-%d %H:%M:%S']:
            try:
                dt = datetime.datetime.strptime(time_str, fmt)
                break
            except ValueError:
                continue
        else:
            # 如果都不匹配，使用smart_to_datetime作为后备（智能时间转换器）
            import warnings
            warnings.warn(f"时间格式 {time_str} 无法识别，使用smart_to_datetime自动检测", UserWarning)
            dt = smart_to_datetime(time_str, format=None)
        
        # 检查是否为日期格式（即时间部分为00:00:00）
        if is_end_time and dt.time() == datetime.time.min:
            # 如果是结束时间且解析为00:00:00，说明用户输入的是日期格式
            # 需要将其调整为当天的最后时刻
            from datetime import timedelta
            dt = dt + timedelta(days=1) - timedelta(microseconds=1)
            logger.debug(LogTarget.FILE, f"检测到日期格式的结束时间，调整为当天最后时刻: {dt}")
        
        return dt
    except Exception as e:
        logger.warning(LogTarget.FILE, f"解析时间字符串 {time_str} 失败: {e}")
        return None


# 时间过滤函数已统一到utils.time_utils模块，删除重复实现
# 使用: from utils.time_utils import filter_data_by_time_range
def filter_data_by_time_range_deprecated(
    df: pd.DataFrame,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None
) -> pd.DataFrame:
    """
    根据时间范围过滤数据，保持原始数据结构不变

    Args:
        df: 要过滤的数据
        start_time: 开始时间 (格式: "YYYYMMDD" 或 "YYYYMMDDHHMMSS")
        end_time: 结束时间 (格式: "YYYYMMDD" 或 "YYYYMMDDHHMMSS")
                  如果是日期格式(YYYYMMDD)，会自动包含该天的全部时间

    Returns:
        pd.DataFrame: 过滤后的数据，保持原始列结构

    Note:
        - 统一使用time列的原始时间戳数值进行过滤，不进行格式转换
        - 过滤完成后保持原始数据结构，不添加额外的列
        - 支持毫秒级和秒级时间戳的自动识别和处理
        - 简化处理逻辑，提高性能
    """
    logger.debug(LogTarget.FILE, f"开始过滤时间范围 - 开始时间: {start_time}, 结束时间: {end_time}")
    
    if df is None or df.empty:
        logger.debug(LogTarget.FILE, "输入数据为空，直接返回")
        return df
    
    if not start_time and not end_time:
        logger.debug(LogTarget.FILE, f"未指定时间范围，直接返回原始数据 ({len(df)} 行)")
        return df
    
    try:
        # 使用新的时间解析函数
        start_dt = _parse_time_range(start_time, is_end_time=False) if start_time else None
        end_dt = _parse_time_range(end_time, is_end_time=True) if end_time else None
        
        # 记录解析结果
        if start_dt:
            logger.debug(LogTarget.FILE, f"解析开始时间: {start_time} -> {start_dt}")
        if end_dt:
            logger.debug(LogTarget.FILE, f"解析结束时间: {end_time} -> {end_dt}")
        
        # 确保有一个有效的时间范围
        if not start_dt and not end_dt:
            logger.warning(LogTarget.FILE, "无法解析时间范围，返回原始数据")
            return df
        
        # 检查是否有time列（统一使用原始时间戳进行过滤）
        has_time_column = 'time' in df.columns
        logger.debug(LogTarget.FILE, f"数据是否有time列: {has_time_column}")

        # 如果有time列，直接使用时间戳数值进行过滤
        if has_time_column:
            try:
                # 检查time列的类型
                time_dtype = df['time'].dtype
                logger.debug(LogTarget.FILE, f"time列类型: {time_dtype}")

                # 统一使用数值时间戳进行过滤（支持智能转换）
                if pd.api.types.is_numeric_dtype(time_dtype):
                    # 数值类型，直接处理
                    time_series = df['time']
                    logger.debug(LogTarget.FILE, f"time列已是数值类型: {time_dtype}")
                elif pd.api.types.is_object_dtype(time_dtype):
                    # object类型，尝试转换为数值
                    logger.debug(LogTarget.FILE, f"time列是object类型，尝试转换为数值格式")
                    try:
                        time_series = pd.to_numeric(df['time'], errors='coerce')
                        failed_count = time_series.isna().sum()
                        if failed_count > 0:
                            logger.warning(LogTarget.FILE, f"time列转换失败 {failed_count} 个值，返回原始数据")
                            filtered_df = df
                            time_series = None
                        else:
                            logger.debug(LogTarget.FILE, f"time列成功转换为数值格式")
                    except Exception as e:
                        logger.warning(LogTarget.FILE, f"time列数值转换异常: {e}，返回原始数据")
                        filtered_df = df
                        time_series = None
                else:
                    logger.warning(LogTarget.FILE, f"time列类型不支持: {time_dtype}，返回原始数据")
                    filtered_df = df
                    time_series = None

                # 如果成功获得数值时间序列，进行过滤
                if time_series is not None:
                    # 检查是毫秒还是秒级时间戳
                    first_ts = time_series.iloc[0]
                    logger.debug(LogTarget.FILE, f"第一个时间戳值: {first_ts}, 类型: {type(first_ts)}")

                    # 将时间字符串转换为时间戳进行比较
                    if start_dt:
                        # 检查start_dt是否为异常时间（如1750年）
                        if start_dt.year < 1980:
                            logger.warning(LogTarget.FILE, f"检测到异常的开始时间: {start_dt}，跳过时间戳过滤")
                            start_ts = None
                        else:
                            if len(str(first_ts)) > 10:  # 毫秒时间戳
                                start_ts = int(start_dt.timestamp() * 1000)
                                logger.debug(LogTarget.FILE, f"开始时间转换为毫秒时间戳: {start_ts}")
                            else:  # 秒时间戳
                                start_ts = int(start_dt.timestamp())
                                logger.debug(LogTarget.FILE, f"开始时间转换为秒时间戳: {start_ts}")
                    else:
                        start_ts = None

                    if end_dt:
                        # 检查end_dt是否为异常时间
                        if end_dt.year < 1980 or end_dt.year > 2100:
                            logger.warning(LogTarget.FILE, f"检测到异常的结束时间: {end_dt}，跳过时间戳过滤")
                            end_ts = None
                        else:
                            if len(str(first_ts)) > 10:  # 毫秒时间戳
                                end_ts = int(end_dt.timestamp() * 1000)
                                logger.debug(LogTarget.FILE, f"结束时间转换为毫秒时间戳: {end_ts}")
                            else:  # 秒时间戳
                                end_ts = int(end_dt.timestamp())
                                logger.debug(LogTarget.FILE, f"结束时间转换为秒时间戳: {end_ts}")
                    else:
                        end_ts = None

                    # 根据时间戳过滤，保持原始数据结构
                    if start_ts and end_ts:
                        filtered_df = df[(time_series >= start_ts) & (time_series <= end_ts)]
                        logger.debug(LogTarget.FILE, f"按开始和结束时间戳过滤: {start_ts} 到 {end_ts}, 结果: {len(filtered_df)} 行")
                    elif start_ts:
                        filtered_df = df[time_series >= start_ts]
                        logger.debug(LogTarget.FILE, f"仅按开始时间戳过滤: {start_ts}, 结果: {len(filtered_df)} 行")
                    elif end_ts:
                        filtered_df = df[time_series <= end_ts]
                        logger.debug(LogTarget.FILE, f"仅按结束时间戳过滤: {end_ts}, 结果: {len(filtered_df)} 行")
                    else:
                        filtered_df = df
                        logger.debug(LogTarget.FILE, "没有有效的时间戳过滤条件，返回原始数据")

                    logger.debug(LogTarget.FILE, "使用时间戳数值过滤完成，保持原始数据结构")

            except Exception as e:
                logger.warning(LogTarget.FILE, f"时间戳过滤失败: {e}")
                # 如果转换失败，返回原始数据
                filtered_df = df
        else:
            logger.debug(LogTarget.FILE, "没有time列，返回原始数据")
            filtered_df = df
        
        # 记录过滤结果
        logger.debug(LogTarget.FILE, f"时间范围过滤完成: 原始数据 {len(df)} 行 -> 过滤后 {len(filtered_df)} 行")
        return filtered_df
    
    except Exception as e:
        logger.error(LogTarget.FILE, f"过滤时间范围时出错: {e}")
        logger.debug(LogTarget.FILE, f"异常详情: {str(e)}, 异常类型: {type(e).__name__}")
        return df  # 出错时返回原始数据


# read_symbol_data函数已删除 - DRY原则清理：无价值包装函数，直接使用向量化读取


def read_multiple_symbols(
    data_root: str,
    symbols: List[str],
    period: str,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    columns: Optional[List[str]] = None
) -> Dict[str, Optional[pd.DataFrame]]:
    """
    读取多个股票代码的数据

    Args:
        data_root: 数据根目录
        symbols: 股票代码列表，例如 ["600000.SH", "000001.SZ"]
        period: 数据周期，例如 "1d", "1m", "tick"
        start_time: 开始时间 (格式: "YYYYMMDD" 或 "YYYYMMDDHHMMSS")
        end_time: 结束时间 (格式: "YYYYMMDD" 或 "YYYYMMDDHHMMSS")
        columns: 要读取的列名列表，默认为None(读取所有列)

    Returns:
        Dict[str, Optional[pd.DataFrame]]: 股票代码到数据的映射字典
    """
    logger.debug(LogTarget.FILE, f"开始读取多个股票数据 - 数据根目录: {data_root}, 股票代码列表: {symbols}, 周期: {period}, 开始时间: {start_time}, 结束时间: {end_time}")
    
    try:
        result = {}
        for i, symbol in enumerate(symbols):
            logger.debug(LogTarget.FILE, f"读取股票 [{i+1}/{len(symbols)}]: {symbol}")
            try:
                df = read_symbol_data(
                    data_root=data_root,
                    symbol=symbol,
                    period=period,
                    start_time=start_time,
                    end_time=end_time,
                    columns=columns
                )
                
                if df is not None:
                    logger.debug(LogTarget.FILE, f"成功读取到股票 {symbol} 的数据，行数: {len(df)}")
                else:
                    logger.debug(LogTarget.FILE, f"未读取到股票 {symbol} 的数据")
                
                result[symbol] = df
            except Exception as e:
                logger.error(LogTarget.FILE, f"读取股票 {symbol} 数据时出错: {e}", exc_info=True)
                result[symbol] = None
        
        logger.debug(LogTarget.FILE, f"完成多个股票数据读取，共 {len(symbols)} 个股票，成功读取 {sum(1 for df in result.values() if df is not None)} 个")
        return result
    
    except Exception as e:
        logger.error(LogTarget.FILE, f"读取多股票数据失败: {e}", exc_info=True)
        return {}


def find_available_data(
    data_root: str,
    market: Optional[str] = None,
    code: Optional[str] = None, 
    period: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    在指定目录中查找可用的数据文件（优化版：批量日志控制）

    Args:
        data_root: 数据根目录
        market: 市场代码过滤，如 "SH", "SZ"
        code: 股票代码过滤，如 "600000"
        period: 数据周期过滤，如 "1d", "1m", "tick"

    Returns:
        List[Dict[str, Any]]: 符合条件的数据文件信息列表
    """
    # 启动批量扫描日志控制
    operation_name = f"可用数据查找"
    if market and code:
        operation_name += f"({market}.{code})"
    elif market:
        operation_name += f"({market}市场)"
    elif code:
        operation_name += f"({code}代码)"

    _start_data_scan(operation_name)

    try:
        result = []
        partition_files = []  # 初始化partition_files变量
        
        # 构建搜索路径
        search_path = data_root
        logger.debug(LogTarget.FILE, f"搜索路径: {search_path}")
        
        # 市场过滤
        if market:
            logger.debug(LogTarget.FILE, f"使用市场过滤: {market}")
            market_dirs = [os.path.join(search_path, market)]
        else:
            logger.debug(LogTarget.FILE, f"无市场过滤，搜索所有市场目录")
            market_dirs = [
                d for d in glob.glob(os.path.join(search_path, "*")) 
                if os.path.isdir(d)
            ]
            logger.debug(LogTarget.FILE, f"找到 {len(market_dirs)} 个市场目录")
        
        # 遍历市场目录
        for market_dir in market_dirs:
            market_name = os.path.basename(market_dir)
            _update_scan_progress('market', market_name)

            # 代码过滤
            if code:
                code_dirs = [os.path.join(market_dir, code)]
            else:
                code_dirs = [
                    d for d in glob.glob(os.path.join(market_dir, "*"))
                    if os.path.isdir(d)
                ]

            # 遍历代码目录
            for code_dir in code_dirs:
                code_name = os.path.basename(code_dir)
                _update_scan_progress('code', code_name)
                
                # 周期过滤
                if period:
                    period_dirs = [os.path.join(code_dir, period)]
                else:
                    period_dirs = [
                        d for d in glob.glob(os.path.join(code_dir, "*"))
                        if os.path.isdir(d)
                    ]

                # 遍历周期目录
                for period_dir in period_dirs:
                    period_name = os.path.basename(period_dir)
                    _update_scan_progress('period', period_name)
                    
                    # 检查是否是分区目录结构
                    if period_name.lower() == 'tick':
                        # Tick数据按日分区
                        year_dirs = [
                            d for d in glob.glob(os.path.join(period_dir, "*")) 
                            if (os.path.isdir(d) and 
                                os.path.basename(d).isdigit())
                        ]
                        
                        for year_dir in year_dirs:
                            year = os.path.basename(year_dir)
                            _update_scan_progress('year', year)

                            # 对于tick数据，检查月份子目录
                            if period_name.lower() == 'tick':
                                month_dirs = [
                                    d for d in glob.glob(os.path.join(year_dir, "*"))
                                    if os.path.isdir(d) and os.path.basename(d).isdigit()
                                ]

                                for month_dir in sorted(month_dirs):
                                    month = os.path.basename(month_dir)

                                    # 获取日期文件
                                    day_files = glob.glob(os.path.join(month_dir, "*.parquet"))

                                    # 更新月份扫描进度（包含文件数量）
                                    _update_scan_progress('month', f"{year}/{month}", len(day_files))

                                    partition_files.extend(day_files)
                                
                                # 为tick数据创建file_info记录
                                if month_dirs:  # 只有存在月份目录时才创建记录
                                    # 计算该年份tick数据的总大小
                                    total_size = 0
                                    latest_modified = 0
                                    
                                    for month_dir in month_dirs:
                                        day_files = glob.glob(os.path.join(month_dir, "*.parquet"))
                                        for day_file in day_files:
                                            try:
                                                file_size = os.path.getsize(day_file)
                                                file_mtime = os.path.getmtime(day_file)
                                                total_size += file_size
                                                latest_modified = max(latest_modified, file_mtime)
                                            except (OSError, IOError):
                                                continue
                                    
                                    # 创建tick数据的file_info记录
                                    file_info = {
                                        "market": market_name,
                                        "code": code_name,
                                        "period": period_name,  # 这里是'tick'
                                        "year": year,
                                        "path": year_dir,  # tick数据的年份目录路径
                                        "size": total_size,
                                        "last_modified": datetime.fromtimestamp(
                                            latest_modified
                                        ).strftime("%Y-%m-%d %H:%M:%S") if latest_modified > 0 else "未知",
                                        "full_symbol": f"{code_name}.{market_name}"
                                    }
                                    
                                    result.append(file_info)
                                    # 移除单独的file_info创建日志，统一在汇总中显示
                            else:
                                # 对于非tick数据，直接获取年份文件
                                year_files = glob.glob(os.path.join(year_dir, "*.parquet"))
                                # 移除单独的年份文件日志，统一在汇总中显示
                                
                                partition_files.extend(year_files)
                    else:
                        # 其他周期按年分区
                        year_files = glob.glob(os.path.join(period_dir, "*.parquet"))
                        
                        for year_file in year_files:
                            year = os.path.splitext(os.path.basename(year_file))[0]
                            
                            # 获取文件信息
                            file_info = {
                                "market": market_name,
                                "code": code_name,
                                "period": period_name,
                                "year": year,
                                "path": year_file,
                                "size": os.path.getsize(year_file),
                                "last_modified": datetime.fromtimestamp(
                                    os.path.getmtime(year_file)
                                ).strftime("%Y-%m-%d %H:%M:%S"),
                                "full_symbol": f"{code_name}.{market_name}"
                            }
                            
                            result.append(file_info)
                
                # 检查旧格式文件（向后兼容）
                if period:
                    legacy_files = glob.glob(
                        os.path.join(code_dir, f"{period}.parquet")
                    )
                else:
                    legacy_files = glob.glob(os.path.join(code_dir, "*.parquet"))
                
                for legacy_file in legacy_files:
                    period_name = os.path.splitext(os.path.basename(legacy_file))[0]
                    
                    # 获取文件信息
                    file_info = {
                        "market": market_name,
                        "code": code_name,
                        "period": period_name,
                        "format": "legacy",
                        "path": legacy_file,
                        "size": os.path.getsize(legacy_file),
                        "last_modified": datetime.fromtimestamp(
                            os.path.getmtime(legacy_file)
                        ).strftime("%Y-%m-%d %H:%M:%S"),
                        "full_symbol": f"{code_name}.{market_name}"
                    }
                    
                    result.append(file_info)

        # 输出批量扫描汇总日志
        _end_data_scan(operation_name)

        return sorted(result, key=lambda x: (x["market"], x["code"], x["period"]))

    except Exception as e:
        logger.error(LogTarget.FILE, f"查找可用数据时出错: {e}")
        # 即使出错也要结束扫描日志
        _end_data_scan(operation_name)
        return []


def summarize_data_directory(data_root: str) -> Dict[str, Dict[str, Dict[str, Any]]]:
    """
    汇总数据目录，获取所有可用的数据文件信息（优化版：批量日志控制）

    Args:
        data_root: 数据根目录

    Returns:
        Dict[str, Dict[str, Dict[str, Any]]]: 市场->代码->周期的嵌套字典，包含数据文件信息
    """
    # 启动批量扫描日志控制
    _start_data_scan("数据目录汇总")

    try:
        result = {}

        # 获取所有市场目录
        market_dirs = [
            d for d in glob.glob(os.path.join(data_root, "*"))
            if os.path.isdir(d)
        ]

        # 遍历市场目录
        for market_dir in sorted(market_dirs):
            market_name = os.path.basename(market_dir)
            _update_scan_progress('market', market_name)
            
            result[market_name] = {}
            
            # 获取所有代码目录
            code_dirs = [
                d for d in glob.glob(os.path.join(market_dir, "*"))
                if os.path.isdir(d)
            ]

            # 遍历代码目录
            for code_dir in sorted(code_dirs):
                code_name = os.path.basename(code_dir)
                _update_scan_progress('code', code_name)

                result[market_name][code_name] = {}

                # 获取所有周期目录
                period_dirs = [
                    d for d in glob.glob(os.path.join(code_dir, "*"))
                    if os.path.isdir(d)
                ]

                # 遍历周期目录
                for period_dir in sorted(period_dirs):
                    period_name = os.path.basename(period_dir)
                    _update_scan_progress('period', period_name)
                    
                    # 获取该周期的数据文件信息
                    try:
                        # 检查是否为分区结构
                        is_partitioned = False
                        partition_files = []
                        
                        # 检查是否有年份子目录（分区结构）
                        year_dirs = [
                            d for d in glob.glob(os.path.join(period_dir, "*")) 
                            if os.path.isdir(d) and os.path.basename(d).isdigit()
                        ]
                        
                        if year_dirs:
                            logger.debug(LogTarget.FILE, f"检测到分区结构，找到 {len(year_dirs)} 个年份目录")
                            is_partitioned = True
                            
                            # 获取分区文件
                            for year_dir in sorted(year_dirs):
                                year = os.path.basename(year_dir)
                                _update_scan_progress('year', year)

                                # 对于tick数据，检查月份子目录
                                if period_name.lower() == 'tick':
                                    month_dirs = [
                                        d for d in glob.glob(os.path.join(year_dir, "*"))
                                        if os.path.isdir(d) and os.path.basename(d).isdigit()
                                    ]

                                    for month_dir in sorted(month_dirs):
                                        month = os.path.basename(month_dir)

                                        # 获取日期文件
                                        day_files = glob.glob(os.path.join(month_dir, "*.parquet"))

                                        # 更新月份扫描进度（包含文件数量）
                                        _update_scan_progress('month', f"{year}/{month}", len(day_files))

                                        partition_files.extend(day_files)
                                else:
                                    # 对于非tick数据，直接获取年份文件
                                    year_files = glob.glob(os.path.join(year_dir, "*.parquet"))
                                    # 移除单独的年份文件日志，统一在汇总中显示

                                    partition_files.extend(year_files)
                        else:
                            # 检查是否有直接的parquet文件（非分区结构）
                            direct_files = glob.glob(os.path.join(period_dir, "*.parquet"))
                            logger.debug(LogTarget.FILE, f"未检测到分区结构，找到 {len(direct_files)} 个直接文件")
                            
                            if direct_files:
                                partition_files = direct_files
                            else:
                                logger.debug(LogTarget.FILE, f"未找到任何数据文件，跳过该周期")
                                continue
                        
                        # 统计数据文件信息
                        total_size = 0
                        file_count = len(partition_files)
                        latest_file = None
                        latest_timestamp = None
                        
                        logger.debug(LogTarget.FILE, f"开始统计 {file_count} 个数据文件的信息...")
                        
                        for file_path in partition_files:
                            try:
                                # 获取文件大小
                                file_size = os.path.getsize(file_path)
                                total_size += file_size
                                
                                # 获取文件的最后修改时间
                                file_mtime = os.path.getmtime(file_path)
                                
                                # 更新最新文件信息
                                if latest_timestamp is None or file_mtime > latest_timestamp:
                                    latest_timestamp = file_mtime
                                    latest_file = file_path
                            except Exception as e:
                                logger.warning(LogTarget.FILE, f"获取文件信息失败: {file_path}, 错误: {e}")
                        
                        # 尝试获取最新文件的最后一条数据时间戳
                        last_data_timestamp = None
                        if latest_file:
                            logger.debug(LogTarget.FILE, f"尝试获取最新文件的最后一条数据时间戳: {latest_file}")
                            try:
                                last_data_timestamp = read_latest_data_timestamp(latest_file)
                                if last_data_timestamp:
                                    logger.debug(LogTarget.FILE, f"获取到最后一条数据时间戳: {last_data_timestamp}")
                                else:
                                    logger.debug(LogTarget.FILE, f"未获取到最后一条数据时间戳")
                            except Exception as e:
                                logger.warning(LogTarget.FILE, f"获取最后一条数据时间戳失败: {e}")
                        
                        # 记录周期信息
                        period_info = {
                            "file_count": file_count,
                            "total_size": total_size,
                            "total_size_mb": round(total_size / (1024 * 1024), 2),
                            "is_partitioned": is_partitioned,
                            "latest_file": latest_file,
                            "latest_file_time": datetime.fromtimestamp(latest_timestamp).strftime('%Y-%m-%d %H:%M:%S') if latest_timestamp else None,
                            "last_data_timestamp": last_data_timestamp
                        }
                        
                        result[market_name][code_name][period_name] = period_info
                        # 移除单独的周期信息日志，统一在汇总中显示

                    except Exception as e:
                        logger.error(LogTarget.FILE, f"处理周期 {period_name} 时出错: {e}", exc_info=True)
                        result[market_name][code_name][period_name] = {
                            "error": str(e)
                        }

        # 输出批量扫描汇总日志
        _end_data_scan("数据目录汇总")

        return result

    except Exception as e:
        logger.error(LogTarget.FILE, f"汇总数据目录失败: {e}", exc_info=True)
        # 即使出错也要结束扫描日志
        _end_data_scan("数据目录汇总")
        return {}





def read_head_tail_data(
    data_root: str,
    symbol: str,
    period: str,
    head_lines: int = 5,
    tail_lines: int = 5,
    columns: Optional[List[str]] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    mode: str = "both"
) -> Optional[pd.DataFrame]:
    """
    高效读取数据的头部和尾部，而不是读取全部数据
    
    Args:
        data_root: 数据根目录
        symbol: 股票代码，格式为 "code.market"
        period: 数据周期
        head_lines: 需要读取的头部行数
        tail_lines: 需要读取的尾部行数
        columns: 要读取的列，如果为None则读取所有列
        start_time: 开始时间，格式为 'YYYYMMDD'，默认为None表示不限制开始时间
        end_time: 结束时间，格式为 'YYYYMMDD'，默认为None表示不限制结束时间
        mode: 显示模式，"tail"表示只读取尾部数据，"head"表示只读取头部数据，
              "both"表示同时读取头部和尾部数据
        
    Returns:
        pd.DataFrame: 合并的头部和尾部数据，如果没有数据则返回None
    """
    try:
        logger.debug(LogTarget.FILE, f"进入函数: read_head_tail_data")
        logger.debug(
            LogTarget.FILE, 
            f"参数: data_root={data_root}, symbol={symbol}, period={period}, "
            f"head_lines={head_lines}, tail_lines={tail_lines}, columns={columns}, "
            f"start_time={start_time}, end_time={end_time}, mode={mode}"
        )
        
        # 解析股票代码
        if '.' in symbol:
            code, market = symbol.split('.')
            # 确保市场代码格式一致（转换为大写）
            market = market.upper()
            # 确保代码格式一致（转换为小写）
            code = code.lower()
        else:
            logger.warning(LogTarget.FILE, f"无效的股票代码格式: {symbol}")
            return None
        
        # 使用智能文件选择优化头尾数据读取
        logger.debug(LogTarget.FILE, f"使用智能文件选择优化头尾数据读取")

        # 统一使用智能文件选择机制
        if start_time or end_time:
            # 有时间范围限制，使用智能文件选择
            partition_files = _get_target_partition_files(
                symbol=symbol,
                period=period,
                start_time=start_time,
                end_time=end_time,
                data_type="raw"
            )
        else:
            # 没有时间范围限制，获取所有分区文件
            partition_files = _get_all_partition_files(
                symbol=symbol,
                period=period,
                data_type="raw"
            )

        if not partition_files:
            logger.debug(LogTarget.FILE, f"未找到分区文件: {symbol}/{period}")
            return None

        # 如果没有时间范围限制，根据模式智能选择最少必要的文件
        if not (start_time or end_time):
            # 根据模式智能选择最少必要的文件
            if mode == "head":
                # 只选择最早的几个文件（估计需要的文件数）
                estimated_files_needed = min(3, len(partition_files))  # 最多3个文件应该足够获取头部数据
                partition_files = partition_files[:estimated_files_needed]
            elif mode == "tail":
                # 只选择最新的几个文件
                estimated_files_needed = min(3, len(partition_files))
                partition_files = partition_files[-estimated_files_needed:]
            else:  # mode == "both"
                # 选择最早和最新的文件
                if len(partition_files) <= 6:
                    pass  # 使用所有文件
                else:
                    # 选择前3个和后3个文件
                    partition_files = partition_files[:3] + partition_files[-3:]
                    # 去重并保持顺序
                    seen = set()
                    partition_files = [f for f in partition_files if not (f in seen or seen.add(f))]

        logger.debug(LogTarget.FILE, f"智能选择了 {len(partition_files)} 个分区文件进行头尾数据读取")

        # 根据模式决定需要处理的文件
        if mode == "head":
            head_files = partition_files
            tail_files = []
        elif mode == "tail":
            head_files = []
            tail_files = partition_files
        else:  # mode == "both"
            head_files = partition_files
            tail_files = partition_files.copy()
        
        logger.debug(
            LogTarget.FILE, 
            f"可用的文件数: 头部={len(head_files)}, 尾部={len(tail_files)}"
        )
        
        head_df = None
        tail_df = None
        
        # 根据模式决定是否读取头部数据
        if mode in ["head", "both"] and head_lines > 0:
            # 读取头部数据
            head_count = 0
            
            for file_path in head_files:
                if head_count >= head_lines:
                    logger.debug(
                        LogTarget.FILE, 
                        f"已获取足够的头部数据行数: {head_count}，停止读取"
                    )
                    break
                    
                logger.debug(LogTarget.FILE, f"读取头部数据文件: {file_path}")
                try:
                    df = pd.read_parquet(file_path, columns=columns)
                    if df is not None and not df.empty:
                        logger.debug(
                            LogTarget.FILE, 
                            f"文件 {file_path} 读取成功，行数: {len(df)}"
                        )
                        
                        # 不处理时间戳，保留原始数据格式
                        
                        # 数据已按时间戳自然排序，无需额外处理
                        
                        # 应用时间范围过滤
                        if start_time or end_time:
                            df = filter_data_by_time_range(df, start_time, end_time)
                            if df.empty:
                                logger.debug(
                                    LogTarget.FILE,
                                    f"应用时间范围过滤后，文件 {file_path} 无数据"
                                )
                                continue
                        
                        # 获取需要的行数
                        remaining_rows = head_lines - head_count
                        current_head = df.head(remaining_rows)
                        head_count += len(current_head)
                        
                        # 合并数据 - 使用统一索引管理器
                        if head_df is None:
                            head_df = current_head
                        else:
                            from utils.data_processor.index_manager import IndexManager
                            head_df = IndexManager.safe_concat([head_df, current_head])
                            if head_df is None:
                                logger.error("头部数据合并失败，无法继续")
                                return None
                        
                        logger.debug(
                            LogTarget.FILE,
                            f"已处理头部文件，当前头部数据行数: {head_count}，目标: {head_lines}"
                        )
                        
                        if head_count >= head_lines:
                            logger.debug(
                                LogTarget.FILE, 
                                f"已获取足够的头部数据行数: {head_count}"
                            )
                            break
                    else:
                        logger.warning(LogTarget.FILE, f"文件 {file_path} 读取为空")
                except Exception as e:
                    logger.error(LogTarget.FILE, f"读取头部文件 {file_path} 失败: {e}")
            
            if head_count < head_lines:
                logger.warning(
                    LogTarget.FILE, 
                    f"已读取所有可用的头部文件，但只获取到 {head_count} 行数据，"
                    f"少于请求的 {head_lines} 行"
                )
        
        # 根据模式决定是否读取尾部数据
        if mode in ["tail", "both"] and tail_lines > 0:
            # 读取尾部数据
            tail_count = 0
            
            for file_path in reversed(tail_files):  # 从最新的文件开始读取
                if tail_count >= tail_lines:
                    logger.debug(
                        LogTarget.FILE, 
                        f"已获取足够的尾部数据行数: {tail_count}，停止读取"
                    )
                    break
                    
                logger.debug(LogTarget.FILE, f"读取尾部数据文件: {file_path}")
                try:
                    df = pd.read_parquet(file_path, columns=columns)
                    if df is not None and not df.empty:
                        logger.debug(
                            LogTarget.FILE, 
                            f"文件 {file_path} 读取成功，行数: {len(df)}"
                        )
                        
                        # 不处理时间戳，保留原始数据格式
                        
                        # 数据已按时间戳自然排序，无需额外处理
                        
                        # 应用时间范围过滤
                        if start_time or end_time:
                            df = filter_data_by_time_range(df, start_time, end_time)
                            if df.empty:
                                logger.debug(
                                    LogTarget.FILE,
                                    f"应用时间范围过滤后，文件 {file_path} 无数据"
                                )
                                continue
                        
                        # 获取需要的行数
                        remaining_rows = tail_lines - tail_count
                        current_tail = df.tail(remaining_rows)
                        tail_count += len(current_tail)
                        
                        # 合并数据 - 使用统一索引管理器
                        if tail_df is None:
                            tail_df = current_tail
                        else:
                            from utils.data_processor.index_manager import IndexManager
                            tail_df = IndexManager.safe_concat([current_tail, tail_df])
                            if tail_df is None:
                                logger.error("尾部数据合并失败，无法继续")
                                return None
                        
                        logger.debug(
                            LogTarget.FILE,
                            f"已处理尾部文件，当前尾部数据行数: {tail_count}，目标: {tail_lines}"
                        )
                        
                        if tail_count >= tail_lines:
                            logger.debug(
                                LogTarget.FILE, 
                                f"已获取足够的尾部数据行数: {tail_count}"
                            )
                            break
                    else:
                        logger.warning(LogTarget.FILE, f"文件 {file_path} 读取为空")
                except Exception as e:
                    logger.error(LogTarget.FILE, f"读取尾部文件 {file_path} 失败: {e}")
            
            if tail_count < tail_lines:
                logger.warning(
                    LogTarget.FILE, 
                    f"已读取所有可用的尾部文件，但只获取到 {tail_count} 行数据，"
                    f"少于请求的 {tail_lines} 行"
                )
        
        # 合并头部和尾部数据
        if head_df is None and tail_df is None:
            logger.warning(LogTarget.FILE, f"未读取到任何数据: {market}/{code}/{period}")
            return None
        elif head_df is None:
            result_df = tail_df
        elif tail_df is None:
            result_df = head_df
        else:
            # 合并并去重（如果头尾有重叠） - 使用统一索引管理器
            from utils.data_processor.index_manager import IndexManager
            result_df = IndexManager.safe_concat([head_df, tail_df])

            if result_df is None:
                logger.error("头尾数据最终合并失败，无法继续")
                return None

            # 去重处理
            if result_df is not None:
                result_df = result_df[~result_df.index.duplicated(keep='first')]
                # 验证合并后的索引格式
                IndexManager.log_index_info(result_df, "头尾数据合并后")

            # 数据已按时间戳自然排序，无需额外处理
        
        # 记录结果信息
        total_rows = len(result_df) if result_df is not None else 0
        logger.debug(LogTarget.FILE, f"函数: read_head_tail_data 返回数据，行数: {total_rows}")
        
        # 数据时间范围信息已在其他地方记录，此处无需重复处理
        
        return result_df
    except Exception as e:
        logger.error(LogTarget.FILE, f"读取头尾数据失败: {e}", exc_info=True)
        return None


# 示例用法
if __name__ == "__main__":
    # 使用统一的日志系统
    import sys
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from utils.logger import get_unified_logger
    logger = get_unified_logger("parquet_reader")
    
    # 获取数据根目录
    root = get_data_root()
    print(f"数据根目录: {root}")
    
    # 测试读取特定股票的数据
    symbol = "000001.SZ"
    period = "1d"
    print(f"读取 {symbol} 的 {period} 数据...")
    
    # 读取数据
    df = read_symbol_data(root, symbol, period)
    if df is not None and not df.empty:
        print(f"成功读取数据，共 {len(df)} 行")
        # 使用pd_format函数格式化数据
        from data.data_source_manager import pd_format, LogTarget
        formatted_df = pd_format(
            df=df,
            data="head",
            head_rows=5,
            tail_rows=0
        )
        print(f"{symbol} - {period}:\n{formatted_df}")
    else:
        print("未找到数据")
    
    # 测试查找可用数据
    print("\n查找可用数据...")
    files = find_available_data(root)
    print(f"找到 {len(files)} 个数据文件")
    for i, file_info in enumerate(files[:5]):
        print(
            f"{i+1}. {file_info['full_symbol']} - "
            f"{file_info['period']} - {file_info['path']}"
        )
    
    if len(files) > 5:
        print(f"... 还有 {len(files) - 5} 个文件未显示")

    # 测试获取分区文件列表
    print("\n测试获取分区文件列表...")
    partition_files = get_partition_files(root, "SH", "600000", "1d")
    print(f"找到 {len(partition_files)} 个分区文件")
    for i, file_path in enumerate(partition_files):
        print(f"{i+1}. {file_path}")

    # 测试汇总数据目录
    print("\n测试汇总数据目录...")
    summary = summarize_data_directory(root)
    print(f"数据目录汇总完成，共 {len(summary)} 个市场")
    for market, codes in summary.items():
        print(f"\n市场: {market}")
        for code, periods in codes.items():
            print(f"\n代码: {code}")
            for period, info in periods.items():
                print(f"周期: {period}")
                for key, value in info.items():
                    print(f"{key}: {value}")

    # 测试读取分区数据
    print("\n测试读取分区数据...")
    df = read_partitioned_data(root, "SH", "600000", "20250501", "20250531")
    if df is not None and not df.empty:
        print(f"成功读取数据，共 {len(df)} 行")
        # 使用pd_format函数格式化数据
        formatted_df = pd_format(
            df=df,
            data="head",
            head_rows=5,
            tail_rows=0
        )
        print(f"600000.SH - 分区数据:\n{formatted_df}")
    else:
        print("未找到数据")


class EnhancedParquetReader:
    """
    增强的Parquet读取器 - 集成类型安全和性能监控功能

    该类提供类型安全的数据读取和合并功能，严格遵循项目规范。
    """

    def __init__(self, enable_type_checking: bool = True,
                 enable_performance_monitoring: bool = True):
        """
        初始化增强的Parquet读取器

        Args:
            enable_type_checking: 是否启用类型检查
            enable_performance_monitoring: 是否启用性能监控
        """
        self.logger = get_unified_logger(__name__, enhanced=True)
        self.enable_type_checking = enable_type_checking and ENHANCED_FEATURES_AVAILABLE
        self.enable_performance_monitoring = enable_performance_monitoring and ENHANCED_FEATURES_AVAILABLE

        # 初始化增强功能组件
        if ENHANCED_FEATURES_AVAILABLE:
            try:
                # 获取配置
                config = config_manager.get_config()
                self.data_type_manager = DataTypeManager(
                    strict_mode=(config.data_processing.validation_level.value == "strict"),
                    auto_fix=config.data_processing.auto_fix_types
                )
            except Exception as e:
                self.logger.warning(LogTarget.FILE, f"初始化DataTypeManager失败: {e}")
                self.data_type_manager = None
                self.enable_type_checking = False
        else:
            self.data_type_manager = None

        # 统计信息
        self.read_statistics = {
            "total_reads": 0,
            "successful_reads": 0,
            "type_fixes": 0,
            "performance_issues": 0
        }

    def read_partitioned_data(self, data_root: str, symbol: str, period: str,
                            start_time: str = None, end_time: str = None,
                            columns: List[str] = None, data_type: str = "raw",
                            adj_type: str = None) -> Optional[pd.DataFrame]:
        """
        读取分区数据，确保类型安全

        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列
            data_type: 数据类型
            adj_type: 复权类型

        Returns:
            pd.DataFrame: 读取的数据
        """
        self.read_statistics["total_reads"] += 1

        # 创建错误上下文（如果可用）
        context = None
        if ENHANCED_FEATURES_AVAILABLE:
            try:
                context = error_handler.create_context(
                    function_name="read_partitioned_data",
                    module_name="enhanced_parquet_reader",
                    operation="读取分区数据",
                    symbol=symbol,
                    period=period,
                    start_time=start_time,
                    end_time=end_time
                )
            except Exception as e:
                self.logger.debug(LogTarget.FILE, f"创建错误上下文失败: {e}")

        # 启动数据流跟踪（如果可用）
        if ENHANCED_FEATURES_AVAILABLE and debug_system.is_debug_enabled("data_flow"):
            try:
                debug_system.data_flow_tracker.start_flow(
                    f"read_partitioned_data_{symbol}_{period}"
                )
            except Exception as e:
                self.logger.debug(LogTarget.FILE, f"启动数据流跟踪失败: {e}")

        # 启动性能监控（如果可用）
        performance_context = None
        if ENHANCED_FEATURES_AVAILABLE and self.enable_performance_monitoring:
            try:
                performance_context = debug_system.performance_monitor.monitor_operation(
                    f"read_partitioned_data_{symbol}_{period}"
                )
                performance_context.__enter__()
            except Exception as e:
                self.logger.debug(LogTarget.FILE, f"启动性能监控失败: {e}")
                performance_context = None

        try:
            # 使用现有的read_partitioned_data函数
            merged_df = read_partitioned_data(
                data_root=data_root,
                symbol=symbol,
                period=period,
                start_time=start_time,
                end_time=end_time,
                columns=columns,
                data_type=data_type,
                adj_type=adj_type
            )

            # 最终验证（如果启用类型检查）
            if self.enable_type_checking and merged_df is not None and self.data_type_manager:
                self._validate_final_result(merged_df, context)

            # 更新统计信息
            if merged_df is not None:
                self.read_statistics["successful_reads"] += 1

            # 结束数据流跟踪（如果可用）
            if ENHANCED_FEATURES_AVAILABLE and debug_system.is_debug_enabled("data_flow"):
                try:
                    flow_summary = debug_system.data_flow_tracker.end_flow()
                    self.logger.debug(LogTarget.FILE,
                               f"数据流跟踪完成: {flow_summary.get('flow_name', 'unknown')}")
                except Exception as e:
                    self.logger.debug(LogTarget.FILE, f"结束数据流跟踪失败: {e}")

            return merged_df

        except Exception as e:
            # 处理错误（如果可用）
            if ENHANCED_FEATURES_AVAILABLE and context:
                try:
                    error_info = error_handler.handle_error(
                        ErrorCode.FIO_READ_FAILED,
                        f"分区数据读取失败: {str(e)}",
                        context,
                        details=str(e),
                        suggestions=[
                            "检查数据文件是否存在",
                            "验证文件权限",
                            "检查数据格式是否正确"
                        ]
                    )
                except Exception as err_e:
                    self.logger.debug(LogTarget.FILE, f"错误处理失败: {err_e}")

            self.logger.error(LogTarget.FILE, f"读取分区数据时发生异常: {e}")
            return None

        finally:
            # 结束性能监控
            if performance_context:
                try:
                    performance_context.__exit__(None, None, None)
                except Exception as e:
                    self.logger.debug(LogTarget.FILE, f"结束性能监控失败: {e}")

    def _safe_merge_dataframes(self, dfs: List[pd.DataFrame], context) -> Optional[pd.DataFrame]:
        """
        安全合并DataFrame - 集成到现有模块中

        Args:
            dfs: 要合并的DataFrame列表
            context: 错误上下文

        Returns:
            Optional[pd.DataFrame]: 合并后的DataFrame
        """
        if not dfs:
            return None

        if len(dfs) == 1:
            return dfs[0]

        try:
            # 如果有DataTypeManager，使用类型安全合并
            if self.data_type_manager and self.enable_type_checking:
                merged_df = self.data_type_manager.safe_concat_with_type_check(
                    dfs, f"{context.operation if context else 'unknown'}_merge"
                )

                if merged_df is not None:
                    self.logger.debug(LogTarget.FILE,
                               f"使用DataTypeManager成功合并 {len(dfs)} 个DataFrame")
                    return merged_df

            # 回退到IndexManager.safe_concat
            from utils.data_processor.index_manager import IndexManager
            merged_df = IndexManager.safe_concat(dfs)

            self.logger.debug(LogTarget.FILE,
                       f"使用IndexManager成功合并 {len(dfs)} 个DataFrame，结果: {merged_df.shape if merged_df is not None else 'None'}")

            return merged_df

        except Exception as e:
            # 处理合并错误
            if ENHANCED_FEATURES_AVAILABLE and context:
                try:
                    error_info = error_handler.handle_error(
                        ErrorCode.DT_INCONSISTENT_TYPES,
                        f"DataFrame合并失败: {str(e)}",
                        context,
                        details=str(e),
                        suggestions=[
                            "检查数据类型一致性",
                            "使用DataTypeManager修复类型问题",
                            "检查索引格式是否正确"
                        ]
                    )
                except Exception as err_e:
                    self.logger.debug(LogTarget.FILE, f"错误处理失败: {err_e}")

            self.logger.error(LogTarget.FILE, f"DataFrame合并失败: {e}")
            return None

    def _validate_final_result(self, df: pd.DataFrame, context):
        """验证最终结果"""
        if df is None or df.empty or not self.data_type_manager:
            return

        try:
            # 进行最终的类型验证
            analysis = self.data_type_manager.analyze_dataframe_types(df, "final_result")

            if analysis.get("issues"):
                self.logger.warning(LogTarget.FILE,
                             f"最终结果存在类型问题: {len(analysis['issues'])} 个")

                for issue in analysis["issues"]:
                    if issue.get("severity") == "error" and ENHANCED_FEATURES_AVAILABLE and context:
                        try:
                            error_handler.handle_error(
                                ErrorCode.DT_TYPE_MISMATCH,
                                f"最终结果类型问题: {issue.get('description', 'unknown')}",
                                context,
                                suggestions=analysis.get("recommendations", [])
                            )
                        except Exception as e:
                            self.logger.debug(LogTarget.FILE, f"错误处理失败: {e}")
        except Exception as e:
            self.logger.debug(LogTarget.FILE, f"最终结果验证失败: {e}")

    def get_statistics(self) -> Dict[str, Any]:
        """获取读取统计信息"""
        stats = {
            **self.read_statistics,
            "success_rate": (
                self.read_statistics["successful_reads"] /
                max(1, self.read_statistics["total_reads"])
            )
        }

        # 添加类型管理器报告（如果可用）
        if self.data_type_manager:
            try:
                stats["type_manager_report"] = self.data_type_manager.get_conversion_report()
            except Exception as e:
                self.logger.debug(LogTarget.FILE, f"获取类型管理器报告失败: {e}")

        return stats