# 复权功能用户指南

## 概述

本系统提供了完整的股票复权数据处理功能，支持前复权和后复权计算。复权功能采用分层架构设计，将原始数据和复权因子作为基础数据永久存储，复权价格作为派生数据智能缓存，实现高性能的动态复权数据合成。

## 核心特性

### 🏗️ 分层架构设计
- **基础数据层**: 原始价格数据 + 复权因子数据（永久存储）
- **派生数据层**: 复权价格数据（智能缓存）
- **接口层**: 统一的复权数据合成接口

### ⚡ 高性能特性
- **智能缓存**: 双层缓存（内存+磁盘），LRU淘汰策略
- **向量化计算**: 基于pandas的高效复权算法
- **批量处理**: 支持多股票并发复权计算
- **增量更新**: 复权因子数据增量获取和更新

### 🔧 算法支持
- **等比前复权**: 基于xtquant的process_forward_ratio算法
- **标准前复权**: 基于xtquant的process_forward算法
- **后复权**: 预留接口，后续扩展

### 📊 向量化读取器集成
- **高性能读取**: 向量化读取器现已支持复权数据，提供2-5倍性能提升
- **统一接口**: 通过dividend_type参数控制复权类型，无需额外处理步骤
- **智能缓存**: 复权数据自动缓存，重复读取性能更优
- **时间过滤**: 支持时间范围过滤与复权功能的完美结合

## 快速开始

### 基本使用

```python
# 方式1: 使用统一接口（推荐）
from utils.data_processor.adjustment import adjustment_synthesizer

# 合成前复权数据
adjusted_data = adjustment_synthesizer.synthesize_adjusted_data(
    symbol="000001.SZ",
    price_data=raw_data,
    dividend_type="front",  # "front"前复权, "back"后复权, "none"原始数据
    method="ratio"  # "ratio"等比复权, "standard"标准复权
)

# 方式2: 使用向量化读取器（推荐，高性能）
from data.storage.vectorized_reader import read_partitioned_data_vectorized

# 直接读取复权数据
df = read_partitioned_data_vectorized(
    data_root="data/parquet",
    symbol="000001.SZ",
    period="1d",
    start_time="20240101",
    end_time="20241231",
    dividend_type="front"  # 新增参数，支持复权数据读取
)

# 方式3: 使用数据读取接口
from data.core.operations import read_data

# 直接读取复权数据
df = read_data(
    symbol="000001.SZ",
    period="1d",
    dividend_type="front",  # 新增参数
    start_time="20240101",
    end_time="20241231"
)
```

### 批量处理

```python
# 批量合成多只股票的复权数据
stock_data = {
    "000001.SZ": raw_data_1,
    "000002.SZ": raw_data_2,
    "600000.SH": raw_data_3
}

batch_result = adjustment_synthesizer.batch_synthesize_adjusted_data(
    stock_data=stock_data,
    dividend_type="front",
    use_cache=True,
    max_workers=4
)
```

## 周期合成中的复权应用

### 1. 单次周期合成

```python
from data.core.operations import synthesize_data

# 合成前复权的5分钟数据
result = synthesize_data(
    symbols=["000001.SZ", "600000.SH"],
    source_period="1m",
    target_period="5m",
    start_date="20240101",
    end_date="20241231",
    dividend_type="front",  # 使用前复权数据
    show_data=True
)
```

### 2. 批量周期合成

```python
from utils.data_processor.period_handler import synthesize_from_local_data

# 批量合成多只股票的复权数据
result = synthesize_from_local_data(
    symbols=["000001.SZ", "600000.SH", "000002.SZ"],
    source_period="1d",
    target_period="1w",
    start_time="20240101",
    end_time="20241231",
    dividend_type="front",  # 前复权
    real_time_save=True
)
```

### 3. 批量脚本配置

在 `data/批量合成历史数据.py` 中配置复权类型：

```python
# 复权配置
dividend_type = "front"  # 前复权

# 目标周期配置
target_periods = ["3m", "5m", "15m", "30m", "1h"]

# 生成配置（自动包含复权设置）
synthesis_configs = generate_synthesis_configs(
    target_periods=target_periods,
    dividend_type=dividend_type
)
```

### 4. 复权数据对比

```python
# 对比不同复权类型的合成结果
dividend_types = ["none", "front", "back"]
results = {}

for div_type in dividend_types:
    result = synthesize_data(
        symbols=["000001.SZ"],
        source_period="1d",
        target_period="1w",
        dividend_type=div_type
    )
    results[div_type] = result

# 分析不同复权类型的价格差异
for div_type, result in results.items():
    if result["success"]:
        data = result["data"]["000001.SZ"]
        print(f"{div_type}复权周收盘价范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
```

## 详细功能说明

### 1. 复权因子管理

```python
from utils.data_processor.adjustment import dividend_factor_storage

# 获取复权因子数据
factors = dividend_factor_storage.query_dividend_factors(
    stock_code="000001.SZ",
    start_date="20240101",
    end_date="20241231"
)

# 更新复权因子数据
success = dividend_factor_storage.update_dividend_factors(
    stock_code="000001.SZ",
    force_update=False
)

# 获取存储信息
info = dividend_factor_storage.get_storage_info()
```

### 2. 复权计算引擎

```python
from utils.data_processor.adjustment import forward_adjustment_engine

# 等比前复权计算
ratio_result = forward_adjustment_engine.process_forward_ratio(
    quote_datas=price_data,
    divid_datas=dividend_factors
)

# 标准前复权计算
standard_result = forward_adjustment_engine.process_forward(
    quote_datas=price_data,
    divid_datas=dividend_factors
)

# 统一接口
adjusted_data = forward_adjustment_engine.calculate_forward_adjustment(
    price_data=price_data,
    dividend_factors=dividend_factors,
    method="ratio"  # 或 "standard"
)
```

### 3. 缓存管理（已更新）

**注意：独立缓存系统已移除，现在使用VectorizedDataReader内置缓存**

```python
from data.storage.unified_data_accessor import get_stock_data

# 数据访问自动使用内置缓存，无需手动管理
data = get_stock_data("000001.SZ", "1d", dividend_type="front")

# 缓存由VectorizedDataReader自动管理，提供最佳性能
# 无需手动清理或失效缓存，系统会自动优化
```

### 4. 系统状态监控

```python
# 获取系统整体状态
status = adjustment_synthesizer.get_system_status()
print(f"系统状态: {status['status']}")
print(f"复权因子存储: {status['dividend_factor_storage']}")
print(f"数据访问器: {status['data_accessor']}")
```

## 配置选项

### 数据访问配置（已更新）

```python
from data.storage.unified_data_accessor import UnifiedDataAccessor

# 自定义缓存配置
custom_cache = AdjustmentPriceCache(
    memory_capacity=200,  # 内存缓存容量
    disk_cache_dir="custom/cache/path",  # 磁盘缓存目录
    cache_ttl_hours=48  # 缓存有效期（小时）
)
```

### 计算引擎配置

```python
# 批量计算配置
batch_result = forward_adjustment_engine.batch_calculate_forward_adjustment(
    stock_price_data=stock_data,
    stock_dividend_factors=dividend_data,
    method="ratio",
    max_workers=8  # 并发数
)
```

## 性能优化建议

### 1. 缓存策略
- **启用缓存**: 对于重复查询的数据，启用缓存可显著提升性能
- **合理设置TTL**: 根据数据更新频率设置合适的缓存有效期
- **定期清理**: 定期清理过期缓存，避免磁盘空间浪费

### 2. 批量处理
- **批量合成**: 对于多只股票，使用批量接口比单独处理效率更高
- **并发控制**: 根据系统资源合理设置max_workers参数
- **内存管理**: 处理大量数据时注意内存使用情况

### 3. 数据管理
- **增量更新**: 使用增量更新机制，避免重复获取已有数据
- **分区存储**: 复权因子数据按股票代码分区存储，提升查询效率

## 故障排除

### 常见问题

1. **复权因子获取失败**
   ```python
   # 检查xtquant连接
   try:
       import xtquant.xtdata as xtdata
       # 测试连接
   except ImportError:
       print("xtquant未安装或配置错误")
   ```

2. **缓存空间不足**
   ```python
   # 清理缓存
   adjustment_price_cache.clear_expired_cache()
   ```

3. **计算结果异常**
   ```python
   # 检查计算结果
   if adjusted_data is None or adjusted_data.empty:
       print("复权计算失败，返回空数据")
   elif adjusted_data.shape != original_data.shape:
       print("复权后数据形状不一致")
   ```

### 日志调试

```python
from utils.logger import get_unified_logger

# 启用详细日志
logger = get_unified_logger(__name__, enhanced=True)
logger.setLevel(logging.DEBUG)
```

## 最佳实践

### 1. 数据存储策略
- **存储原始数据**: 默认存储原始数据（dividend_type="none"）
- **动态合成**: 在回测或分析时动态合成复权数据
- **定期更新**: 定期更新复权因子数据，确保数据准确性

### 2. 性能优化
- **使用向量化读取器**: 优先使用read_partitioned_data_vectorized，获得2-5倍性能提升
- **预热缓存**: 对于常用股票，可预先计算并缓存复权数据
- **分批处理**: 大量股票处理时，分批进行避免内存溢出
- **监控性能**: 定期检查缓存命中率和计算耗时
- **智能文件选择**: 向量化读取器自动选择最少必要文件，避免读取无关数据

### 3. 错误处理
- **降级策略**: 复权计算失败时返回原始数据
- **日志记录**: 详细记录操作日志，便于问题排查
- **数据验证**: 对计算结果进行验证，确保数据质量

## 性能对比

### 向量化读取器复权功能性能测试

| 功能 | 传统方式 | 向量化读取器 | 性能提升 |
|------|----------|-------------|----------|
| 原始数据读取 | 24秒 | 0.14秒 | 171倍 |
| 前复权数据读取 | 26秒 | 0.18秒 | 144倍 |
| 后复权数据读取 | 25秒 | 0.16秒 | 156倍 |
| 文件选择 | 1354个文件 | 2个文件 | 677倍减少 |

### 最佳实践建议

1. **优先使用向量化读取器**: 获得100倍以上性能提升
2. **合理设置时间范围**: 精确的时间范围可进一步提升性能
3. **启用智能缓存**: 重复读取同一股票复权数据时性能更优
4. **批量处理**: 多股票处理时使用batch_synthesize_adjusted_data

## 版本信息

- **当前版本**: 2.3.0
- **创建日期**: 2025-01-27
- **最后更新**: 2025-01-18
- **兼容性**: Python 3.8+, pandas 1.3+
- **新增功能**: 向量化读取器复权支持

## 更新历史

- **v2.2.0 (2025-07-29)**: 删除复权结果验证功能，解决数组比较错误，简化系统复杂度
- **v2.1.0 (2025-07-28)**: 修复索引类型不匹配和数组判断错误
- **v1.0.0 (2025-01-27)**: 初始版本发布

## 技术支持

如有问题或建议，请查看：
- 项目文档: `docs/`目录
- 测试用例: `tests/test_adjustment_functionality.py`
- 源代码: `utils/data_processor/adjustment/`目录
