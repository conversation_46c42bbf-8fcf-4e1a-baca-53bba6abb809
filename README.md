# 量化交易系统

## 项目概述
这是一个基于Python的量化交易系统，用于股票、期货等金融产品的数据获取、分析、回测和实盘交易。

## 主要功能
- 数据获取与存储
- 多周期数据合成
- 复权数据处理
- 策略开发与测试
- 回测系统
- 风险控制
- 交易执行
- 性能监控

## 🔧 配置管理重构 (2025-08-02)

### 统一配置架构
- **唯一配置源**：所有配置统一在 `config/settings.py` 中管理
- **DRY原则遵循**：彻底删除重复配置文件，消除配置冲突
- **简化访问**：直接从 `config.settings` 导入配置，无需复杂的配置管理器
- **核心指导思维**：删除后备方案，保持代码简洁，减少维护成本

### 配置文件结构
```
config/
├── settings.py             # 唯一配置文件（所有配置）
├── symbols.py              # 股票代码配置
├── *.backup               # 已删除文件的备份
└── [已删除的重复配置文件]
    ├── system_config.json      # 已删除
    ├── data_source_config.py   # 已删除
    └── utils/config/config_manager.py  # 已删除
```

### 配置使用方式
```python
# 统一配置导入方式
from config.settings import (
    LOG_LEVEL, DATA_ROOT, DEFAULT_TIMEZONE,
    DATA_VALIDATION_LEVEL, TIME_CONVERTER_CACHE_SIZE
)

# 不再使用复杂的配置管理器
# ❌ 旧方式：from utils.config.config_manager import config_manager
# ✅ 新方式：from config.settings import SETTING_NAME
```

## 🚀 多周期合成系统重构 (2025-07-30)

### 核心架构升级
- **统一存储架构**：支持raw/（原始数据）和adjusted/（复权数据）分离存储
- **智能路径管理**：PathManagerV2自动区分期货和股票，期货不支持复权处理
- **高性能缓存**：双层缓存架构（内存LRU + 磁盘持久化）
- **PyArrow优化**：解决大数据集处理中的数据类型转换问题
- **完全重构**：遵循核心指导思维，删除所有后备方案，保持代码简洁

### 数据存储结构
```
data/
├── raw/                    # 原始数据
│   ├── SZ/000001/1d/      # 股票原始数据
│   ├── SH/600000/1d/      # 股票原始数据
│   └── DF/pp00/1d/        # 期货原始数据
└── adjusted/               # 复权数据（新格式）
    ├── front/              # 前复权数据
    │   ├── SZ/000001/1d/  # 前复权股票数据
    │   └── SH/600000/1d/  # 前复权股票数据
    └── back/               # 后复权数据
        ├── SZ/000001/1d/  # 后复权股票数据
        └── SH/600000/1d/  # 后复权股票数据
```

### 快速使用
```python
from data.storage import save_to_partition, read_partitioned_data

# 保存原始数据
save_to_partition(df, "000001.SZ", "1d", data_type="raw")

# 保存复权数据
save_to_partition(df, "000001.SZ", "1d", data_type="adjusted", adj_type="front")

# 读取原始数据
raw_data = read_partitioned_data("D:/data", "000001.SZ", "1d", data_type="raw")

# 读取复权数据
adj_data = read_partitioned_data("D:/data", "000001.SZ", "1d",
                                data_type="adjusted", adj_type="front")
```

### 系统测试结果
- **集成测试通过率**：100% (5/5)
- **路径管理器测试**：✅ 通过
- **数据存储测试**：✅ 通过
- **数据读取测试**：✅ 通过
- **缓存系统测试**：✅ 通过
- **端到端集成测试**：✅ 通过

## 🎉 智能时间转换器 (2025-01-18)

### 核心特性
- **解决时区问题**：彻底解决smart_to_datetime的8小时偏移问题
- **智能类型检测**：自动识别毫秒时间戳、秒时间戳、字符串格式
- **统一管理接口**：一处修改影响全项目，便于维护
- **完全兼容**：与pandas接口兼容，支持所有参数
- **错误处理**：支持raise/coerce/ignore三种错误处理模式

### 快速使用
```python
from utils.smart_time_converter import smart_to_datetime

# 自动识别毫秒时间戳
result = smart_to_datetime([1737158400000, 1737158460000])
# 输出: 2025-01-18 08:00:00, 2025-01-18 08:01:00 (正确时区)

# 自动识别秒时间戳
result = smart_to_datetime([1737158400, 1737158460])

# 自动识别字符串格式
result = smart_to_datetime(['20250118080000', '20250118080100'])

# 显式指定类型（更高性能）
result = smart_to_datetime(timestamps, unit='ms')
```

### 替换说明
项目中的`pd.to_datetime`调用正在逐步替换为`smart_to_datetime`，以解决时区问题并提供更好的类型检测。

### 性能表现
- **转换速度**：0.0015毫秒/个时间戳
- **内存使用**：约0.06KB/个时间戳
- **错误处理**：完善的边界情况处理
- **测试覆盖**：26个测试确保功能稳定

## 数据存储模块修复记录

### 2025-06-21 修复 (3)
1. 修复了数据读取模块中的参数问题：
   - 在`data/data_source_manager.py`中删除了两个`get_local_data`函数中的`process_time`参数
   - 修改了调用`read_partitioned_data`函数的代码，删除了`process_time`参数的传递
   - 解决了读取本地数据时出现的"read_partitioned_data() got an unexpected keyword argument 'process_time'"错误

2. 主要改进：
   - 使代码与项目保留原始数据格式的方向保持一致
   - 提高了代码的一致性和可维护性
   - 解决了因参数不匹配导致的运行时错误
   - 确保了数据读取功能的正常工作

### 2025-06-21 修复 (2)
1. 修复了数据下载模块中的函数导入和方法调用问题：
   - 在`data/data_source_manager.py`中添加了对`merge_dataframes`和`validate_merged_data`函数的导入
   - 替换所有局部导入的`get_data_time_range`函数，使用全局导入的函数
   - 将所有`self.display_dataframe`方法调用替换为使用`pd_format`函数
   - 解决了下载数据时出现的"name 'merge_dataframes' is not defined"和"'DataSourceManager' object has no attribute 'display_dataframe'"错误

2. 主要改进：
   - 修复了数据合并功能，确保增量下载时能正确合并数据
   - 修复了数据显示功能，使用统一的`pd_format`函数格式化DataFrame
   - 提高了系统稳定性，避免因函数未定义或方法不存在导致的异常
   - 确保了增量下载和数据显示功能的正常工作

### 2025-06-21 修复 (1)
1. 修复了数据下载模块中的函数导入问题：
   - 在`data/data_source_manager.py`中添加了对`validate_date_format`和`adjust_to_trading_day`函数的导入
   - 解决了下载数据时出现的"name 'validate_date_format' is not defined"错误
   - 确保了日期验证功能正常工作

2. 主要改进：
   - 修复了日期验证功能，确保下载数据时能正确验证日期格式
   - 提高了系统稳定性，避免因函数未定义导致的异常
   - 确保了增量下载功能的正常工作

### 2025-06-15 更新 (2)
1. 优化了路径管理器的文件检查逻辑：
   - 修改了`get_partition_files`函数，移除优先检查年份文件的逻辑
   - 直接根据周期类型检查分区目录，使所有周期都采用与tick相同的处理逻辑
   - 简化了文件检查逻辑，提高了日志可读性
   - 详细文档请参考[路径管理器更新说明](docs/path_manager_update.md)

### 2025-06-15 更新 (1)
1. 优化了数据加载功能，提高了性能：
   - 新增`read_head_tail_data`函数，支持只读取数据的头部和尾部，而不是读取全部数据
   - 优化`read_data`函数，支持高效加载模式，只读取需要显示的数据行
   - 添加兼容层，支持旧的参数格式，保持向后兼容性

2. 主要改进：
   - 显著提高了数据加载性能，对于只显示头部或尾部数据的场景，性能提升7-15倍
   - 减少了内存使用，只加载必要的数据分区
   - 确保了向后兼容性，现有代码可以继续使用旧的参数格式
   - 详细文档请参考[高效数据加载功能文档](docs/efficient_data_loading.md)

### 2025-06-12 修复 (4)
1. 修复了数据下载模块中的日期比较逻辑问题：
   - 撤销了在`xtquant_data.py`中添加的重复日期比较逻辑
   - 改进了`data_source_manager.py`中的日期比较逻辑，确保日期格式转换和比较是一致的
   - 添加了更详细的日志记录，以便于调试和理解日期比较的过程
   - 确保日期比较在增量下载前进行，避免无效的时间范围下载

2. 主要改进：
   - 保持代码结构清晰，逻辑在合适的层级处理，避免重复代码
   - 增强了日期比较的可靠性，确保格式一致性
   - 提高了系统的整体稳定性和效率
   - 添加了更详细的日志记录，便于问题排查

### 2025-06-12 修复 (3)
1. 修复了数据存储和下载模块中的三个关键问题：
   - 修复了`xtquant_data.py`中的增量下载逻辑，添加日期比较，避免无效的时间范围下载
   - 修复了`parquet_storage.py`中未导入`merge_dataframes`函数的问题
   - 修复了`data_source_manager.py`中未导入`get_save_path`函数的问题

2. 主要改进：
   - 增强了增量下载的稳定性，当本地数据的最后日期晚于或等于用户请求的结束日期时，跳过下载
   - 解决了数据合并时出现的"name 'merge_dataframes' is not defined"错误
   - 解决了检查本地数据时出现的"name 'get_save_path' is not defined"错误
   - 提高了系统的整体稳定性和可靠性

### 2025-06-12 修复 (2)
1. 修复了数据存储模块中的数据格式转换问题：
   - 移除了`parquet_storage.py`中`save_data_by_partition`函数自动将time列转换为DatetimeIndex的代码
   - 保留原始数据格式，确保存储的数据与原始数据保持一致
   - 改进了按年份分组的逻辑，在不修改原始数据格式的情况下实现分区存储

2. 主要改进：
   - 保留了原始数据格式，避免了不必要的格式转换
   - 确保了存储和读取的数据格式一致
   - 提高了数据处理的可靠性和准确性

### 2025-06-12 修复
1. 修复了数据存储模块中的数据格式转换问题：
   - 移除了`merge_dataframes`函数中对`convert_timestamps_to_string`的调用，保留原始数据格式
   - 修改了`save_data_to_parquet`函数，不再将datetime64类型转换为字符串
   - 确保存储的是原始数据，特别是保留时间戳的原始格式

2. 主要改进：
   - 保留了原始数据格式，避免了不必要的转换
   - 提高了数据存储和读取的一致性
   - 确保了PyArrow能够正确处理datetime64类型

### 2025-06-11 修复
1. 修复了数据存储模块中的以下问题：
   - 修复了`get_partition_files`函数，使其能正确识别分区文件
   - 改进了`read_partitioned_data`函数，正确处理市场和代码
   - 添加了`process_timestamp`函数，确保正确处理时间戳
   - 修复了`get_local_data`函数，使其能正确调用`read_partitioned_data`函数并返回数据
   - 添加了`get_data_root`函数，方便调试和访问数据根目录

2. 主要改进：
   - 增强了时间戳处理能力，支持毫秒和秒级时间戳的自动识别
   - 改进了分区文件识别逻辑，支持年份文件和分区目录两种存储方式
   - 优化了日志输出，增加了更详细的调试信息
   - 增强了错误处理，提高了系统稳定性

### 2025-06-13 修复
1. 修复了日志格式化器中的缩进级别控制机制问题：
   - 在`formatters.py`中为`TableMarkdownFormatter`和`EnhancedFormatter`的`_get_level_prefix`方法添加了最大前缀长度限制（5个#）
   - 增强了`_update_indentation`和`_update_level`方法，使其能识别错误日志并正确结束当前操作
   - 在异常处理代码中添加了明确的操作结束标记，确保缩进级别正确减少

2. 修复了`parquet_storage.py`中未导入`merge_dataframes`函数的问题：
   - 添加了`from utils.data_processor import merge_dataframes`导入语句
   - 确保在异常处理时记录清晰的日志，并正确标记操作结束

3. 主要改进：
   - 解决了日志中过多#号的问题，提高了日志可读性
   - 解决了数据合并时出现的"name 'merge_dataframes' is not defined"错误
   - 增强了错误处理机制，即使在异常情况下也能保持正确的日志格式
   - 提高了系统的整体稳定性和可靠性

## 安装与配置

### 环境要求
- Python 3.8+
- pandas, numpy, matplotlib
- pyarrow, fastparquet
- TA-Lib

### 安装步骤
1. 克隆仓库
```bash
git clone https://github.com/yourusername/quant.git
cd quant
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

## 使用示例

### 数据获取
```python
from data.data_source_manager import get_local_data

# 获取本地数据
data = get_local_data(['000001.SZ'], '1d', '20200101', '20200131')
print(data['000001.SZ'].head())
```

### 回测
```python
from backtest.engine import BacktestEngine
from strategy.sample_strategy import SampleStrategy

# 创建回测引擎
engine = BacktestEngine(
    strategy=SampleStrategy(),
    start_date='20200101',
    end_date='20201231',
    initial_capital=1000000
)

# 运行回测
results = engine.run()
engine.plot_results()
```

## 项目结构
- `data/`: 数据获取和存储模块
- `strategy/`: 策略实现
- `backtest/`: 回测系统
- `risk/`: 风险控制模块
- `trading/`: 交易执行模块
- `utils/`: 工具函数
- `config/`: 配置文件
- `tests/`: 测试代码

## 模块结构

- **数据模块**: 提供数据获取、处理和存储功能，详见 [数据模块文档](./data/README.md)
- **回测模块**: 提供策略回测功能
- **交易模块**: 提供实时交易功能
- **风险管理模块**: 提供风险控制功能
- **监控模块**: 提供系统监控功能

## 安装与使用

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行回测

```bash
python run_backtest.py
```

## 功能亮点

### 数据功能

- **多源数据获取**: 支持从多个数据源获取行情数据
- **本地数据管理**: 提供便捷的数据获取、查询和管理功能
- **数据目录浏览**: 新增`list_data`和`summarize_data`功能，便于浏览和管理外部数据目录

### 日志系统

### 特点

- **统一配置**: 一次配置，全局生效，简化日志管理
- **增强格式**: 支持彩色输出、模块名着色和图标前缀
- **表格式布局**: 提供整齐一致的日志布局，支持模块名右对齐
- **灵活输出**: 可同时输出到控制台和文件，或指定单一目标
- **分离内容**: 支持标题和内容分离，可为不同部分设置不同输出目标
- **自动清理**: 自动清理过期日志文件，避免占用过多磁盘空间
- **性能优化**: 使用轮转文件处理器，支持日志压缩

### 使用示例

```python
from utils.logger import setup_unified_logging, get_unified_logger, LogTarget

# 配置日志系统
setup_unified_logging(log_level='info')

# 获取日志记录器
logger = get_unified_logger('my_module')

# 基本用法
logger.debug("这是一条调试信息")
logger.info("这是一条信息")
logger.warning("这是一条警告")
logger.error("这是一条错误")
logger.critical("这是一条严重错误")

# 指定输出目标
logger.info(LogTarget.FILE, "这条信息只记录到文件")
logger.info(LogTarget.CONSOLE, "这条信息只显示在控制台")
logger.info(LogTarget.BOTH, "这条信息同时记录到文件和显示在控制台")

# 标题和内容分离
logger.info(
    title="数据处理开始",  # 标题
    content="正在处理数据...",  # 内容
    title_target=LogTarget.BOTH,  # 标题输出到文件和控制台
    content_target=LogTarget.FILE  # 内容只输出到文件
)
```

更多详细信息，请参阅 [日志系统文档](./utils/logger/README.md)。

### 设置日志级别

如果你发现日志输出太多，可以通过以下方式设置日志级别来屏蔽不重要的日志：

```python
from utils.logger import setup_unified_logging

# 设置日志级别为WARNING，屏蔽INFO和DEBUG级别的日志
setup_unified_logging(log_level='warning')

# 可选的日志级别有：
# - 'debug'：显示所有日志（最详细）
# - 'info'：显示INFO及以上级别的日志（默认）
# - 'warning'：只显示WARNING、ERROR和CRITICAL级别的日志
# - 'error'：只显示ERROR和CRITICAL级别的日志
# - 'critical'：只显示CRITICAL级别的日志（最简洁）
```

这样可以有效减少日志输出，尤其是在处理大量数据或者日期类型转换时产生的调试信息。

### 策略功能

- **多策略支持**: 支持多种量化交易策略
- **参数优化**: 支持策略参数优化

### 回测功能

- **高性能回测**: 高效的回测引擎
- **详细报告**: 生成详细的回测报告

### 任务管理功能

- **任务检查点系统**: 提供完整的任务进度跟踪、管理和恢复功能
- **工具调用监控**: 监控并限制工具API调用次数，防止超出配额
- **自动保存管理**: 自动跟踪和保存文件变更，便于恢复
- **检查点管理**: 创建和管理任务检查点，与Git集成
- **任务报告生成**: 生成结构化的任务进度报告，支持Markdown和HTML格式

## 获取帮助

如需更多帮助，请查阅各模块文档或联系开发团队。

# 最近修复和优化

## 数据读取性能优化（2025-08-01）

### 问题描述
数据读取性能严重下降，读取小时间范围的tick数据需要24秒，原因是系统读取了所有分区文件后再进行时间过滤。

### 解决方案
实现了智能的时间范围文件过滤功能：

- **文件级别过滤**：在读取前根据时间范围过滤分区文件
- **智能日期提取**：从文件路径中提取日期信息进行比较
- **性能监控**：添加详细的过滤日志和性能统计

### 优化效果
- **文件数量减少99.0%**：从1354个文件减少到13个文件
- **数据量减少99.1%**：从680万行减少到6.5万行
- **读取时间减少98.6%**：从24.16秒减少到0.34秒

## 数据格式化功能优化

### 问题描述
原有的`display_dataframe`和`_display_df_part`函数存在以下问题：
- 功能重复，代码冗余
- 直接将格式化结果输出到日志，而不是返回格式化后的DataFrame
- 参数设计不够灵活，难以满足不同的数据显示需求

### 解决方案
新增了`pd_format`函数，用于替代原有的`display_dataframe`和`_display_df_part`函数：

- **简化接口**：提供了更简洁的接口，使用单一函数替代原有的两个函数
- **返回结果**：不再直接输出到日志，而是返回格式化后的DataFrame，便于后续处理
- **灵活显示**：支持多种显示模式（"head"、"tail"、"both"、"all"），满足不同的数据查看需求
- **可配置行数**：可以自定义显示的头部和尾部行数
- **保留格式**：保持原始数据的格式，不进行不必要的转换

### 使用示例

```python
from data.data_source_manager import pd_format

# 只显示头部数据
head_df = pd_format(df, data="head", rows=10)
print(head_df)

# 只显示尾部数据
tail_df = pd_format(df, data="tail", rows=10)
print(tail_df)

# 同时显示头部和尾部数据
both_df = pd_format(df, data="both", head_rows=5, tail_rows=5)
print(both_df)

# 显示全部数据
all_df = pd_format(df, data="all")
print(all_df)
```

### 影响
这一优化简化了数据显示代码，提高了代码的可维护性和可扩展性，同时为用户提供了更灵活的数据查看选项。

## 数据源管理器Bug修复

### 问题修复
- 修复了数据源管理器中未导入`get_save_path`函数导致的错误，该错误会在检查本地数据时触发
- 解决了增量数据更新过程中可能出现的模块导入问题

### 调试日志增强
- 增加了详细的模块导入跟踪日志，帮助识别导入相关问题
- 在关键操作点添加了上下文信息，使问题定位更加准确
- 增强了错误处理，添加了更多异常上下文信息和堆栈追踪
- 改进了数据处理流程的日志记录，包括数据合并和保存前的详细信息

### 影响
这些修复和优化提高了系统在处理大量数据时的稳定性和可靠性，特别是在增量更新过程中，能更好地追踪和解决潜在问题。

# 数据增量更新优化

## 问题描述

在原有的增量更新机制中，即使只需要更新最新的数据（如2025年），系统也会重写所有涉及到的年份分区文件（包括2018年等历史数据），这导致了不必要的I/O操作和性能开销。

## 优化方案

### 1. 数据比对功能

在`data/storage/parquet_storage.py`中的`save_to_partition`函数中增加了数据比对功能：
- 在保存数据前，比较新旧数据是否有实际变化
- 只有当数据有变化时才进行文件重写操作
- 通过比较行数和内容来确定数据是否需要更新

优化效果：
- 避免对没有变化的数据进行重复写入
- 减少磁盘I/O操作
- 提高增量更新的效率

### 2. 选择性数据加载

在`data/data_source_manager.py`中的数据合并逻辑中增加了选择性数据加载功能：
- 在合并数据前，根据时间范围过滤本地数据
- 只加载与新数据时间范围重叠或接近的历史数据进行合并
- 默认回退30天以确保有足够的重叠数据

优化效果：
- 减少内存使用量
- 加快数据处理速度
- 只处理必要的数据

## 使用方法

增量更新的使用方法与之前相同，但现在系统会更智能地处理数据：

```python
# 增量更新数据
download_history_data(
    stock_list=["000001.SZ"],
    period="1m",
    start_time="",  # 空字符串表示使用本地最新数据时间
    end_time="20250603",
    incremental=True  # 启用增量更新
)
```

## 性能改进

- 大幅减少磁盘写入操作
- 降低内存使用峰值
- 提高数据处理速度
- 减少不必要的文件备份和重写

## 注意事项

- 数据比对功能可能会增加少量的CPU使用率，但总体上减少了I/O操作，提高了性能
- 选择性数据加载的回退天数(30天)可以根据实际需求调整 

# Parquet存储增强

## 问题描述

在保存DataFrame到Parquet文件时，当数据中包含Timestamp对象时，PyArrow会抛出类型转换错误：
```
Expected bytes, got a 'Timestamp' object
```
这个问题主要出现在处理日期时间数据和时间序列时，特别是在合并新旧数据后保存到Parquet文件的过程中。

另外，当DataFrame的索引是DatetimeIndex类型且索引名称与现有列名冲突时，会导致以下错误：
```
cannot insert time, already exists
```

## 解决方案

在`data/storage/parquet_storage.py`中的`save_data_to_parquet`函数中增加了以下增强功能：

### 1. 预处理时间类型数据

- 在转换为PyArrow表之前，预处理DataFrame中的日期时间类型
- 自动检测并转换包含Timestamp对象的列
- 特别处理DatetimeIndex索引，将其转换为字符串列

### 2. 多级错误处理和恢复

- 添加了多层次的错误处理和修复尝试
- 在转换失败时提供详细的诊断信息
- 实现了更智能的后备保存方案

### 3. 增强的类型安全性

- 添加了对各种时间类型数据的安全处理
- 确保数据类型兼容性，避免转换错误
- 保留原始数据的语义，同时确保存储兼容性

### 4. 递归转换Timestamp对象

- 新增递归转换功能，彻底处理所有嵌套的Timestamp对象
- 深度扫描DataFrame的所有部分，确保没有遗漏任何Timestamp对象
- 智能处理各种数据结构中的时间类型数据

### 5. 索引名称冲突处理

- 自动检测并解决索引名称与现有列的冲突
- 智能重命名索引，确保不会与现有列冲突
- 在重置索引前先将DatetimeIndex转换为字符串，避免类型转换问题

### 6. CSV中间格式后备方案

- 当所有直接保存方法都失败时，尝试通过CSV中间格式保存数据

# 数据存储原始格式保留

## 问题描述

在之前的实现中，当存储tick以上的数据（如1m、1h、1d）到分区文件时，系统会自动将Timestamp对象转换为字符串格式，导致原始数据格式被改变。这种转换会影响数据的可用性和一致性，特别是在后续处理和分析中。

## 解决方案

在`data/storage/parquet_storage.py`中进行了以下修改：

### 1. 移除自动时间格式转换

- 删除了在`save_data_to_parquet`函数中对`convert_timestamps_recursively`的调用
- 移除了相关的时间格式转换代码
- 保留了原始数据格式，不再自动将Timestamp对象转换为字符串

### 2. 保留检测功能但不进行转换

- 在`save_to_partition`函数中保留了对Timestamp对象的检测功能
- 将原来的转换操作替换为日志记录，不再修改数据
- 确保数据以原始格式存储

### 3. 保留必要的日志记录

- 保留了数据类型和结构的详细日志记录
- 改进了日志消息，明确指出系统现在保留原始数据格式

## 优势

- **数据一致性**: 存储的数据与原始数据保持相同的格式和类型
- **减少数据转换**: 避免了不必要的格式转换，减少了数据处理开销
- **提高可用性**: 便于后续分析和处理，无需反向转换
- **兼容性更好**: 与原始数据源格式保持一致，便于跨系统交互

## 注意事项

- 如果确实需要转换数据格式，可以在读取数据后显式调用相关转换函数
- 这一更改主要影响tick以上的数据格式，tick数据本身的存储格式不受影响
- 先将数据保存为CSV文件，再读取并转换为Parquet格式
- 提供最后一道防线，确保数据能够成功保存

### 7. 数据合并前的Timestamp处理

- 在`utils/data_processor/data_merger.py`中添加了数据合并前的预处理功能
- 在合并新旧数据前，先将所有Timestamp对象转换为字符串格式
- 避免合并后的DataFrame中出现混合类型导致的转换错误

### 8. 详细诊断日志

- 添加了更详细的日志输出，记录数据类型和转换过程
- 在关键步骤添加了数据诊断信息，便于定位问题
- 在检测到Timestamp对象时提供明确的警告

## 使用方法

使用方式与之前相同，但现在系统能够更可靠地处理包含时间戳和日期时间的数据：

```python
# 保存包含时间戳的数据
save_data_to_parquet(
    df=dataframe_with_timestamps,
    file_path="path/to/save.parquet",
    engine="pyarrow",
    metadata={"source": "example"}
)
```

## 改进效果

- 解决了Timestamp对象导致的保存错误
- 解决了索引名称冲突问题
- 提高了数据存储的可靠性
- 增强了错误诊断和恢复能力
- 保持了数据的完整性和一致性
- 提供了多层次的后备保存方案，确保数据不会丢失
- 从根源上解决了数据合并过程中产生的Timestamp问题 

### Debug日志文件

系统支持将所有级别的日志（包括DEBUG级别）写入到专门的debug日志文件中，便于问题排查和分析难以复现的问题：

```python
from utils.logger import setup_unified_logging, LogTarget
from config.settings import (
    DEBUG_LOG_ENABLED, DEBUG_LOG_LEVEL, DEBUG_LOG_MAX_SIZE,
    DEBUG_LOG_BACKUP_COUNT, DEBUG_LOG_DAYS_TO_KEEP
)

# 启用debug日志文件
setup_unified_logging(
    log_level="info",  # 普通日志级别为INFO
    enable_debug_log=DEBUG_LOG_ENABLED,  # 启用debug日志文件
    debug_log_level="debug",  # debug日志文件的日志级别为DEBUG
    debug_log_max_size=20 * 1024 * 1024,  # 设置debug日志文件大小限制为20MB
    debug_log_backup_count=10,  # 保留10个备份文件
    debug_log_days_to_keep=30  # 保留30天的debug日志
)
```

系统将同时维护两种日志文件：
1. 普通日志文件（quant_YYYYMMDD.log）：记录INFO及以上级别的日志，主要用于常规监控和查看。
2. Debug日志文件（quant_debug_YYYYMMDD.log）：记录所有级别的日志，包括详细的DEBUG信息，用于问题排查和分析难以复现的问题。

### 日志查看工具

系统提供了命令行工具用于查看和分析日志文件，支持按级别、模块、时间范围、关键字等条件过滤日志：

```bash
# 查看最新的普通日志
python view_logs.py

# 查看最新的debug日志
python view_logs.py -t debug

# 查看指定日期的debug日志
python view_logs.py -t debug -d 20250610

# 查看包含关键字的日志
python view_logs.py -k "错误"

# 查看指定模块的日志
python view_logs.py -m data_source_manager

# 查看指定级别的日志
python view_logs.py -l error

# 查看指定时间范围的日志
python view_logs.py -s "2025-06-10 08:00:00" -e "2025-06-10 09:00:00"

# 以JSON格式输出日志
python view_logs.py -o json
```

完整的命令行参数说明：

```
参数:
  -f, --file FILE           日志文件路径
  -t, --type {normal,debug} 日志类型，normal或debug
  -l, --level LEVEL         日志级别，如debug、info、warning、error、critical
  -m, --module MODULE       模块名
  -i, --task-id TASK_ID     任务ID
  -s, --start-time START_TIME 开始时间，格式为YYYY-MM-DD HH:MM:SS
  -e, --end-time END_TIME   结束时间，格式为YYYY-MM-DD HH:MM:SS
  -k, --keyword KEYWORD     关键字
  -n, --max-lines MAX_LINES 最大行数，默认为1000
  -o, --output {text,json,csv} 输出格式，默认为text
  -r, --recent RECENT       最近的日志文件数量
  -d, --date DATE           日期，格式为YYYYMMDD
``` 

## 模块说明

### 数据存储模块 (data/storage)

该模块提供数据存储和读取功能，支持多种格式和分区存储。主要包含以下功能：

- **路径管理**：提供基于分区的数据存储路径管理功能
- **数据读取**：从分区存储结构中读取数据
- **数据存储**：将数据保存到分区文件中

最近更新：
- 为存储模块所有过程添加了详细的调试日志，包括函数开始、结束以及关键步骤，便于问题排查和性能分析 

## 调试日志

系统现已添加详细的调试日志功能，可以帮助开发者追踪程序执行流程、定位问题和理解代码行为。

### 日志特点

- **多级日志**: 使用不同的日志级别（DEBUG、INFO、WARNING、ERROR、CRITICAL）区分不同重要程度的信息
- **详细记录**: 在函数开始、关键步骤和函数结束时记录详细信息
- **参数跟踪**: 记录函数的输入参数和返回值
- **错误处理**: 在异常处理中添加详细的错误日志，包括堆栈跟踪
- **性能监控**: 记录耗时操作的执行时间

### 已添加日志的模块

1. **存储模块** (`data/storage/`)
   - `path_manager.py`: 路径管理相关函数的日志
   - `parquet_reader.py`: Parquet文件读取操作的日志
   - `parquet_storage.py`: Parquet文件存储操作的日志

2. **数据模块** (`data/`)
   - `__init__.py`: 模块初始化和导入过程的日志
   - `data_commands.py`: 数据命令函数的日志，包括下载和读取数据
   - `data_main.py`: 主要功能函数和命令行接口的日志
   - `data_source_manager.py`: 数据源管理相关函数的日志

### 如何使用日志

1. **查看日志文件**:
   - 日志文件保存在项目根目录的`logs`文件夹中
   - 一般日志文件名格式为`quant_YYYYMMDD.log`
   - 调试日志文件名格式为`quant_debug_YYYYMMDD.log`

2. **控制日志级别**:
   - 可以通过修改`utils/logger/manager.py`中的`setup_unified_logging`函数参数控制日志级别
   - 默认情况下，INFO及以上级别的日志会显示在控制台，DEBUG及以上级别的日志会保存到文件

3. **添加新的日志**:
   - 使用`logger.debug(LogTarget.FILE, "消息")`添加调试日志
   - 使用`logger.info(LogTarget.FILE, "消息")`添加信息日志
   - 使用`logger.warning(LogTarget.FILE, "消息")`添加警告日志
   - 使用`logger.error(LogTarget.FILE, "消息", exc_info=True)`添加错误日志（带堆栈跟踪） 

# 系统修复与优化

## 缩进错误修复

修复了多个文件中的缩进错误，确保代码能够正常运行：

- 修复了`data/storage/parquet_reader.py`文件中的`except`语句缩进错误
- 修复了`data/data_main.py`文件中`interactive_mode`和`period_synthesis_menu`函数中的缩进问题

## 模块导入优化

修复了模块导入相关的问题：

- 在`data/data_commands.py`中添加了对`validate_date_format`函数的导入，解决了"name 'validate_date_format' is not defined"错误
- 在`data/data_source_manager.py`中添加了对`validate_date_format`和`adjust_to_trading_day`函数的导入
- 优化了`get_unified_logger()`函数的调用，确保提供必要的`name`参数

这些修复确保了系统的稳定性和可靠性，特别是在数据下载和处理过程中。 

# 数据存储模块修复 (2025-06-11)

## 问题描述

在数据存储模块中发现了以下问题：
1. 数据下载和保存功能正常，但`get_local_data`函数无法读取已保存的数据，返回空DataFrame
2. 时间戳转换问题导致无法正确过滤指定时间范围的数据
3. `read_partitioned_data`函数无法正确处理毫秒级时间戳数据

## 解决方案

### 1. 时间戳转换问题修复

在`filter_data_by_time_range`函数中增加了对毫秒级时间戳的正确处理：
- 添加了创建临时datetime列的逻辑，用于正确过滤时间范围
- 增强了对不同时间格式的兼容性处理
- 添加了多层错误处理，确保在各种情况下都能正常工作

### 2. 数据索引处理优化

在`read_partitioned_data`函数中添加了对时间戳的处理：
- 检测数值型时间戳并自动转换为datetime索引
- 确保时间范围过滤能够正确应用于不同格式的数据
- 优化了多分区数据的合并和排序逻辑

### 3. 本地数据读取修复

修复了`get_local_data`函数的工作流程：
- 确保正确调用`read_symbol_data`函数
- 修复了时间范围过滤的应用
- 增强了错误处理和日志记录

## 使用示例

现在可以正确地下载、保存和读取数据：

```python
# 下载并保存数据
download_history_data(["000001.SZ"], "1m", "20250601", "20250610")

# 读取本地数据
data = get_local_data(["000001.SZ"], "1m", "20250601", "20250610")
print(data["000001.SZ"].head())
```

## 影响

这些修复确保了数据下载、保存和读取功能的完整流程正常工作，特别是在处理包含毫秒级时间戳的数据时，能够正确地进行时间范围过滤和数据检索。 

## 最新更新

### 2023-08-23: 存储模块性能优化
- 优化了 Parquet 存储模块的性能，使存储操作速度提高了 3-5 倍
- 简化了数据转换和元数据处理流程
- 保持了完整的 API 兼容性
- 存储操作时间从 >1000ms 降低到 <300ms

### 2025-06-21 修复 (3)
1. 修复了数据读取模块中的参数问题：
   - 在`data/data_source_manager.py`中删除了两个`get_local_data`函数中的`process_time`参数
   - 修改了调用`read_partitioned_data`函数的代码，删除了`process_time`参数的传递
   - 解决了读取本地数据时出现的"read_partitioned_data() got an unexpected keyword argument 'process_time'"错误

# Quant Project

量化交易项目

## 目录结构

```
.
├── data/             # 数据处理模块
├── examples/         # 示例代码
├── strategy/         # 策略模块
├── utils/            # 工具函数
└── README.md         # 项目说明
```

## 示例代码

### xtdata_download.py

简单的xtquant数据下载示例，展示如何下载和获取股票历史数据。

```python
# 使用方法
python examples/xtdata_download.py
```

### xtdata_download_enhanced.py

增强版的xtquant数据下载工具，支持多个股票和不同周期的数据下载。

功能：
- 支持多个股票代码
- 支持不同数据周期（日线、分钟线、小时线）
- 可自定义时间范围和数据字段
- 提供简单易用的API

```python
# 使用方法
python examples/xtdata_download_enhanced.py
```

示例API：
```python
# 获取单个股票数据
stock_data = get_stock_data("000001.SZ", period="1d", days_back=60)

# 获取多个股票数据
stocks_data = get_multiple_stocks_data(
    ["000001.SZ", "600000.SH"], 
    period="1d", 
    days_back=30
)
``` 